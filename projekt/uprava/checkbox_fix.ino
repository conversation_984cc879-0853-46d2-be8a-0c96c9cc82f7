/**
 * Oprava problému s ukládáním hodnot zaškrtávacích políček ve WiFiManageru
 * 
 * Tento kód <PERSON>, jak upravit metody initializeParameters() a saveConfigFromParameters()
 * pro správné ukládání hodnot zaškrtávacích políček.
 */

// ============================================================================ 
// UPRAVENÉ FUNKCE PRO WIFI CONFIGURATION
// ============================================================================ 

void saveConfigFromParameters() {
    Serial.println(F("---"));
    Serial.println(F("Starting to save parameters from web form"));
    Serial.println(F("---"));
    int paramIndex = 0;
    const char* value;

    // General Settings
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: TMEP Server = %s\n", paramIndex - 1, value ? value : "null"); if (value) strcpy(config.serverAddressTmep, value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: ThingSpeak HTTP Key = %s\n", paramIndex - 1, value ? value : "null"); if (value) strcpy(config.thingSpeakApiKey, value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: ThingSpeak Channel ID = %s\n", paramIndex - 1, value ? value : "null"); if (value) strcpy(config.thingSpeakChannelId, value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: Sleep Time = %s\n", paramIndex - 1, value ? value : "null"); if (value) config.sleepTime = atoi(value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: Sensor Type = %s\n", paramIndex - 1, value ? value : "null"); if (value) config.sensorType = atoi(value);

    paramIndex++; // Skip separator 5

    // Server Selection - NOVÝ PŘÍSTUP
    // Nejprve získáme aktuální hodnotu pro porovnání
    uint8_t oldServerSelection = config.serverSendType;
    
    // Resetujeme serverSendType na 0 a pak nastavíme jednotlivé bity podle hodnot checkboxů
    config.serverSendType = 0;
    
    // TMEP
    value = customParams[paramIndex++]->getValue(); 
    Serial.printf("Reading param %d: Send to TMEP? = %s\n", paramIndex - 1, value ? value : "null");
    if (value && strcmp(value, "1") == 0) {
        config.serverSendType |= SERVER_TMEP;
        Serial.println("TMEP odesílání ZAPNUTO");
    } else {
        Serial.println("TMEP odesílání VYPNUTO");
    }
    
    // ThingSpeak HTTP
    value = customParams[paramIndex++]->getValue();
    Serial.printf("Reading param %d: Send to ThingSpeak HTTP? = %s\n", paramIndex - 1, value ? value : "null");
    if (value && strcmp(value, "1") == 0) {
        config.serverSendType |= SERVER_THINGSPEAK;
        Serial.println("ThingSpeak HTTP odesílání ZAPNUTO");
    } else {
        Serial.println("ThingSpeak HTTP odesílání VYPNUTO");
    }
    
    // MQTT
    value = customParams[paramIndex++]->getValue();
    Serial.printf("Reading param %d: Send to MQTT? = %s\n", paramIndex - 1, value ? value : "null");
    if (value && strcmp(value, "1") == 0) {
        config.serverSendType |= SERVER_MQTT;
        Serial.println("MQTT odesílání ZAPNUTO");
    } else {
        Serial.println("MQTT odesílání VYPNUTO");
    }
    
    Serial.printf(">>> VÝSLEDEK: serverSendType = %d (bitmask: TMEP=%d, ThingSpeak=%d, MQTT=%d)\n", 
                 config.serverSendType,
                 (config.serverSendType & SERVER_TMEP) ? 1 : 0,
                 (config.serverSendType & SERVER_THINGSPEAK) ? 1 : 0,
                 (config.serverSendType & SERVER_MQTT) ? 1 : 0);
    
    Serial.printf(">>> ZMĚNA: Stará hodnota = %d, Nová hodnota = %d\n", oldServerSelection, config.serverSendType);

    paramIndex++; // Skip separator 9

    // Hardware Pins
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: SDA Pin = %s\n", paramIndex - 1, value ? value : "null"); if (value) config.sdaPin = atoi(value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: SCL Pin = %s\n", paramIndex - 1, value ? value : "null"); if (value) config.sclPin = atoi(value);

    paramIndex++; // Skip separator 12

    // ThingSpeak Fields
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: TS Field Temp = %s\n", paramIndex - 1, value ? value : "null"); if (value) config.tsFieldTemp = atoi(value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: TS Field Hum = %s\n", paramIndex - 1, value ? value : "null"); if (value) config.tsFieldHum = atoi(value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: TS Field Press = %s\n", paramIndex - 1, value ? value : "null"); if (value) config.tsFieldPress = atoi(value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: TS Field CO2 = %s\n", paramIndex - 1, value ? value : "null"); if (value) config.tsFieldCO2 = atoi(value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: TS Field Batt = %s\n", paramIndex - 1, value ? value : "null"); if (value) config.tsFieldBatt = atoi(value);

    paramIndex++; // Skip separator 18

    // Generic MQTT Settings
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: MQTT Server = %s\n", paramIndex - 1, value ? value : "null"); if (value) strcpy(config.mqttServer, value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: MQTT Port = %s\n", paramIndex - 1, value ? value : "null"); if (value) config.mqttPort = atoi(value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: MQTT Topic = %s\n", paramIndex - 1, value ? value : "null"); if (value) strcpy(config.mqttTopic, value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: MQTT User = %s\n", paramIndex - 1, value ? value : "null"); if (value) strcpy(config.mqttUser, value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: MQTT Pass = %s\n", paramIndex - 1, value ? value : "null"); if (value) strcpy(config.mqttPass, value);

    paramIndex++; // Skip separator 24

    // ThingSpeak MQTT Profile - NOVÝ PŘÍSTUP
    bool oldTsMqttValue = config.useThingSpeakMqtt;
    
    value = customParams[paramIndex++]->getValue();
    Serial.printf("Reading param %d: Use TS MQTT? = %s\n", paramIndex - 1, value ? value : "null");
    config.useThingSpeakMqtt = (value && strcmp(value, "1") == 0);
    
    Serial.printf("ThingSpeak MQTT profil: %s (změna: %s -> %s)\n", 
                 config.useThingSpeakMqtt ? "ZAPNUTO" : "VYPNUTO",
                 oldTsMqttValue ? "ZAPNUTO" : "VYPNUTO",
                 config.useThingSpeakMqtt ? "ZAPNUTO" : "VYPNUTO");
    
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: TS MQTT ClientID = %s\n", paramIndex - 1, value ? value : "null"); if (value) strcpy(config.thingSpeakMqttClientID, value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: TS MQTT Username = %s\n", paramIndex - 1, value ? value : "null"); if (value) strcpy(config.thingSpeakMqttUsername, value);
    value = customParams[paramIndex++]->getValue(); Serial.printf("Reading param %d: TS MQTT API Key = %s\n", paramIndex - 1, value ? value : "null"); if (value) strcpy(config.thingSpeakMqttApiKey, value);

    Serial.println(F("---"));
    Serial.println(F("All parameters read, attempting to save config to EEPROM"));
    Serial.println(F("---"));
    saveConfig();
    Serial.println(F("---"));
    Serial.println(F("saveConfig() finished. The device will now continue."));
    Serial.println(F("---"));
}

void initializeParameters() {
    if (parametersInitialized) return;

    // Convert numeric values to strings using snprintf
    snprintf(sleepTimeStr, sizeof(sleepTimeStr), "%d", config.sleepTime);
    snprintf(sensorTypeStr, sizeof(sensorTypeStr), "%d", config.sensorType);
    snprintf(sdaPinStr, sizeof(sdaPinStr), "%d", config.sdaPin);
    snprintf(sclPinStr, sizeof(sclPinStr), "%d", config.sclPin);
    snprintf(tsFieldTempStr, sizeof(tsFieldTempStr), "%d", config.tsFieldTemp);
    snprintf(tsFieldHumStr, sizeof(tsFieldHumStr), "%d", config.tsFieldHum);
    snprintf(tsFieldPressStr, sizeof(tsFieldPressStr), "%d", config.tsFieldPress);
    snprintf(tsFieldCO2Str, sizeof(tsFieldCO2Str), "%d", config.tsFieldCO2);
    snprintf(tsFieldBattStr, sizeof(tsFieldBattStr), "%d", config.tsFieldBatt);
    snprintf(mqttPortStr, sizeof(mqttPortStr), "%d", config.mqttPort);

    int paramIndex = 0;

    // General Settings
    customParams[paramIndex++] = new WiFiManagerParameter("tmepServer", "TMEP.CZ address", config.serverAddressTmep, 40);
    customParams[paramIndex++] = new WiFiManagerParameter("thingSpeakKey", "ThingSpeak HTTP Write API Key", config.thingSpeakApiKey, 40);
    customParams[paramIndex++] = new WiFiManagerParameter("thingSpeakChannel", "ThingSpeak Channel ID", config.thingSpeakChannelId, 20);
    customParams[paramIndex++] = new WiFiManagerParameter("sleepTime", "Sleep time (minutes)", sleepTimeStr, 6);
    customParams[paramIndex++] = new WiFiManagerParameter("sensorType", "Sensor type (99=auto)", sensorTypeStr, 2);

    customParams[paramIndex++] = new WiFiManagerParameter("<h3>Send Destinations</h3>"); // Separator & Title

    // NOVÝ PŘÍSTUP: Checkboxy pro výběr serverů
    // TMEP.cz - Přidáme JavaScript pro aktualizaci hodnoty při změně
    bool tmepEnabled = (config.serverSendType & SERVER_TMEP) != 0;
    char tmep_html[128];
    snprintf(tmep_html, sizeof(tmep_html), 
             "type='checkbox' onchange='this.previousSibling.value=this.checked?1:0' %s", 
             tmepEnabled ? "checked" : "");
    
    // Přidáme skryté pole, které bude vždy obsahovat aktuální hodnotu checkboxu
    char tmep_param[256];
    snprintf(tmep_param, sizeof(tmep_param),
             "<input type='hidden' id='%s' name='%s' value='%s'><input %s>",
             "sendTmep", "sendTmep", tmepEnabled ? "1" : "0", tmep_html);
    
    customParams[paramIndex++] = new WiFiManagerParameter("sendTmep", "Send to TMEP.cz (HTTP)", "", 0, tmep_param);
    Serial.printf("Inicializace checkboxu TMEP: %s\n", tmepEnabled ? "ZAŠKRTNUTO" : "NEZAŠKRTNUTO");
    
    // ThingSpeak HTTP
    bool tsEnabled = (config.serverSendType & SERVER_THINGSPEAK) != 0;
    char ts_html[128];
    snprintf(ts_html, sizeof(ts_html), 
             "type='checkbox' onchange='this.previousSibling.value=this.checked?1:0' %s", 
             tsEnabled ? "checked" : "");
    
    char ts_param[256];
    snprintf(ts_param, sizeof(ts_param),
             "<input type='hidden' id='%s' name='%s' value='%s'><input %s>",
             "sendTs", "sendTs", tsEnabled ? "1" : "0", ts_html);
    
    customParams[paramIndex++] = new WiFiManagerParameter("sendTs", "Send to ThingSpeak (HTTP)", "", 0, ts_param);
    Serial.printf("Inicializace checkboxu ThingSpeak: %s\n", tsEnabled ? "ZAŠKRTNUTO" : "NEZAŠKRTNUTO");
    
    // MQTT
    bool mqttEnabled = (config.serverSendType & SERVER_MQTT) != 0;
    char mqtt_html[128];
    snprintf(mqtt_html, sizeof(mqtt_html), 
             "type='checkbox' onchange='this.previousSibling.value=this.checked?1:0' %s", 
             mqttEnabled ? "checked" : "");
    
    char mqtt_param[256];
    snprintf(mqtt_param, sizeof(mqtt_param),
             "<input type='hidden' id='%s' name='%s' value='%s'><input %s>",
             "sendMqtt", "sendMqtt", mqttEnabled ? "1" : "0", mqtt_html);
    
    customParams[paramIndex++] = new WiFiManagerParameter("sendMqtt", "Send to MQTT Broker", "", 0, mqtt_param);
    Serial.printf("Inicializace checkboxu MQTT: %s\n", mqttEnabled ? "ZAŠKRTNUTO" : "NEZAŠKRTNUTO");

    customParams[paramIndex++] = new WiFiManagerParameter("<h3>Hardware Settings</h3>"); // Separator & Title

    // I2C Pins
    customParams[paramIndex++] = new WiFiManagerParameter("sdaPin", "I2C SDA pin", sdaPinStr, 3);
    customParams[paramIndex++] = new WiFiManagerParameter("sclPin", "I2C SCL pin", sclPinStr, 3);

    customParams[paramIndex++] = new WiFiManagerParameter("<h3>ThingSpeak HTTP Field Mapping</h3>"); // Separator & Title

    // ThingSpeak fields
    customParams[paramIndex++] = new WiFiManagerParameter("tsFieldTemp", "Field for Temperature", tsFieldTempStr, 2);
    customParams[paramIndex++] = new WiFiManagerParameter("tsFieldHum", "Field for Humidity", tsFieldHumStr, 2);
    customParams[paramIndex++] = new WiFiManagerParameter("tsFieldPress", "Field for Pressure", tsFieldPressStr, 2);
    customParams[paramIndex++] = new WiFiManagerParameter("tsFieldCO2", "Field for CO2", tsFieldCO2Str, 2);
    customParams[paramIndex++] = new WiFiManagerParameter("tsFieldBatt", "Field for Battery", tsFieldBattStr, 2);

    customParams[paramIndex++] = new WiFiManagerParameter("<h3>Generic MQTT Broker Settings</h3>"); // Separator & Title

    // MQTT parameters
    customParams[paramIndex++] = new WiFiManagerParameter("mqttServer", "Server Address", config.mqttServer, 40);
    customParams[paramIndex++] = new WiFiManagerParameter("mqttPort", "Port", mqttPortStr, 6);
    customParams[paramIndex++] = new WiFiManagerParameter("mqttTopic", "Topic", config.mqttTopic, 40);
    customParams[paramIndex++] = new WiFiManagerParameter("mqttUser", "Username", config.mqttUser, 32);
    customParams[paramIndex++] = new WiFiManagerParameter("mqttPass", "Password", config.mqttPass, 32);

    customParams[paramIndex++] = new WiFiManagerParameter("<h3>ThingSpeak MQTT Profile</h3>"); // Separator & Title

    // NOVÝ PŘÍSTUP: ThingSpeak MQTT Profile checkbox
    char ts_mqtt_html[128];
    snprintf(ts_mqtt_html, sizeof(ts_mqtt_html), 
             "type='checkbox' onchange='this.previousSibling.value=this.checked?1:0' %s", 
             config.useThingSpeakMqtt ? "checked" : "");
    
    char ts_mqtt_param[256];
    snprintf(ts_mqtt_param, sizeof(ts_mqtt_param),
             "<input type='hidden' id='%s' name='%s' value='%s'><input %s>",
             "useTsMqtt", "useTsMqtt", config.useThingSpeakMqtt ? "1" : "0", ts_mqtt_html);
    
    customParams[paramIndex++] = new WiFiManagerParameter("useTsMqtt", "Use ThingSpeak MQTT Profile", "", 0, ts_mqtt_param);
    Serial.printf("Inicializace checkboxu ThingSpeak MQTT: %s\n", config.useThingSpeakMqtt ? "ZAŠKRTNUTO" : "NEZAŠKRTNUTO");
    
    customParams[paramIndex++] = new WiFiManagerParameter("tsMqttClientId", "ClientID", config.thingSpeakMqttClientID, 64);
    customParams[paramIndex++] = new WiFiManagerParameter("tsMqttUser", "Username", config.thingSpeakMqttUsername, 64);
    customParams[paramIndex++] = new WiFiManagerParameter("tsMqttApiKey", "MQTT API Key", config.thingSpeakMqttApiKey, 32);

    parametersInitialized = true;
}
