/*
*  Firmware pro Meteo Mini ESP32-C3 s podporou TMEP.CZ a ThingSpeak
*  Umožňuje flexibilní konfiguraci odesíl<PERSON>í dat
*  Obsahuje OnDemand konfiguraci pomocí tlačítka
*  Firmware vyhledá adresy následujíc<PERSON>ch senzorů:
*
*  - SHT40 (0x44)
*  - SHT40 (0x45)
*  - BME280 (0x76)
*  - BME280 (0x77)
*  - SCD41
*  - DS18B20
*
*  Případně lze typ senzoru vynutit, čí<PERSON>ž se automatické vyhledávání zakáže.
*
*  Podle nalezených senzorů se vyplní GET URL:
*  - SHT40: teplota + vlhkost + napětí baterie
*  - BME280: teplota + vlhkost + tlak + napětí baterie
*  - SCD41: teplota + vlhkost + CO2 + napětí baterie
*  - DS18B20: teplota + napětí baterie
*
*  POZOR: Připojte současně POUZE JEDEN senzor!!!
*/

#include <Arduino.h>
#include <SensirionI2cSht4x.h>
#include <Adafruit_BME280.h>
#include <SensirionI2cScd4x.h>
#include <Wire.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <WiFiManager.h> // https://github.com/tzapu/WiFiManager
#include <DNSServer.h>
#include <WebServer.h>
#include <EEPROM.h>
#include <OneWire.h>
#include <DallasTemperature.h>
#include <esp_sleep.h>
#include <driver/gpio.h>

// Ukládání hodnoty do RTC paměti (přetrvává během deep sleep)
RTC_DATA_ATTR int bootCount = 0;

// Verze firmware
const char FIRMWARE_VERSION[] = "2.2";

// Konfigurace pinů
#define ONE_WIRE_BUS 10 // Pin pro připojení DS18B20 senzoru
#define DIVIDER_RATIO 1.7693877551 // Poměr děliče napětí pro měření baterie
#define PWR_PIN 3 // Pin pro zapínání/vypínání napájení uSup
#define ADC_IN 0 // Analogový vstup pro měření napětí baterie
#define TRIGGER_PIN 2  // Pin pro zapnutí konfiguračního portálu
// Bitová maska pro probuzení z deep sleep je definována přímo v kódu jako 1ULL << TRIGGER_PIN
#define BATTERY_THRESHOLD 3.6 // Prahová hodnota napětí pro detekci napájení z baterie (V)
#define CONFIG_TIMEOUT_BATTERY 30 // Timeout konfiguračního portálu při napájení z baterie (sekundy)
#define CONFIG_TIMEOUT_EXTERNAL 30 // Timeout konfiguračního portálu při externím napájení (sekundy)

// Výchozí hodnoty pro I2C piny
// Konfigurace I2C pinů - nyní nastavitelné v EEPROM
#define DEFAULT_SDA_PIN 19
#define DEFAULT_SCL_PIN 18

// Definice verze konfigurace
#define CONFIG_VERSION 1  // Zvýšit při změně struktury konfigurace

// Definice adres v EEPROM
#define EEPROM_SIZE 512

// Výčet druhů serverů pro odesílání dat
enum ServerType {
  NONE = 0,
  TMEP = 1,
  THINGSPEAK = 2,
  BOTH = 3
};

// Výčet modelů senzorů
enum SensorModel {
    SHT40_0X44 = 0,
    SHT40_0X45 = 1,
    BME280_0X76 = 2,
    BME280_0X77 = 3,
    SCD41_0X62 = 4, // Adresa SCD4x je typicky 0x62
    DS18B20_OW = 5,
    AUTO_DETECT = 99
};

// Struktura pro konfiguraci
struct Config {
  char serverAddressTmep[40];
  char thingSpeakApiKey[40];
  char thingSpeakChannelId[20];
  int sleepTime;
  int sensorType;
  int serverSendType;
  int sdaPin;
  int sclPin;
  int tsFieldTemp;
  int tsFieldHum;
  int tsFieldPress;
  int tsFieldCO2;
  int tsFieldBatt;
  uint8_t configVersion;
};

// Globální instance konfigurace
Config config;

// Výchozí hodnoty pro WiFiManager parametry (použijí se, pokud EEPROM není validní nebo při resetu)
const char* defaultServerAddressTmep = "xny3ef-8khshy.tmep.cz/index.php";
const char* defaultThingSpeakApiKey = "ABS8T4OULVYA1FOL";
const char* defaultThingSpeakChannelId = "845449";
const char* defaultSleepTime = "5";
const char* defaultSensorType = "99"; // AUTO_DETECT
const char* defaultServerSendType = "3"; // BOTH
const char* defaultSdaPin = String(DEFAULT_SDA_PIN).c_str();
const char* defaultSclPin = String(DEFAULT_SCL_PIN).c_str();
const char* defaultTsFieldTemp = "2";
const char* defaultTsFieldHum = "3";
const char* defaultTsFieldPress = "5";
const char* defaultTsFieldCO2 = "6";
const char* defaultTsFieldBatt = "4";


// Funkce pro měření napětí baterie s průměrováním
float getBatteryVoltage() {
  float sum = 0;
  const int samples = 5;
  for (int i = 0; i < samples; i++) {
    sum += analogReadMilliVolts(ADC_IN) * DIVIDER_RATIO / 1000.0; // Použít 1000.0 pro float dělení
    delay(10);
  }
  float voltage = sum / samples;
  Serial.print("Napětí baterie: ");
  Serial.print(voltage, 2); // Výpis na 2 desetinná místa
  Serial.println("V");
  return voltage;
}

// Funkce pro detekci napájení z baterie
bool isRunningOnBattery() {
  float voltage = getBatteryVoltage();
  // Pokud je napětí nižší než prahová hodnota, pravděpodobně běžíme na baterii
  return (voltage < BATTERY_THRESHOLD);
}

// Vytvoření instancí
WiFiManager wm;
SensirionI2cSht4x sht4x;
Adafruit_BME280 bme;
SensirionI2cScd4x SCD4X;
OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature DS18B20(&oneWire);
DeviceAddress sensorAddress;

// Proměnná pro sledování stavu konfiguračního portálu
bool portalRunning = false;

// Rozšířené parametry WiFi manageru
WiFiManagerParameter custom_serverAddressTmep("tmepServer", "TMEP.CZ adresa (max 40 znaků)", defaultServerAddressTmep, 40);
WiFiManagerParameter custom_thingSpeakApiKey("thingSpeakKey", "ThingSpeak API klíč", defaultThingSpeakApiKey, 40);
WiFiManagerParameter custom_thingSpeakChannelId("thingSpeakChannel", "ThingSpeak Channel ID", defaultThingSpeakChannelId, 20);
WiFiManagerParameter custom_sleepTime("sleepTime", "Doba spánku (max 200 minut)", defaultSleepTime, 6);
WiFiManagerParameter custom_sensorType("sensorType", "Typ senzoru (99: auto, 0-5)", defaultSensorType, 2);
WiFiManagerParameter custom_serverSendType("serverType", "Cíl odesílání (0:Žádný, 1:TMEP, 2:ThingSpeak, 3:Oba)", defaultServerSendType, 2);
WiFiManagerParameter custom_sdaPin("sdaPin", "I2C SDA pin", defaultSdaPin, 3);
WiFiManagerParameter custom_sclPin("sclPin", "I2C SCL pin", defaultSclPin, 3);

// Parametry pro ThingSpeak fieldy (kanály)
WiFiManagerParameter custom_tsFieldTemp("tsFieldTemp", "ThingSpeak field pro teplotu (1-8)", defaultTsFieldTemp, 2);
WiFiManagerParameter custom_tsFieldHum("tsFieldHum", "ThingSpeak field pro vlhkost (1-8)", defaultTsFieldHum, 2);
WiFiManagerParameter custom_tsFieldPress("tsFieldPress", "ThingSpeak field pro tlak (1-8)", defaultTsFieldPress, 2);
WiFiManagerParameter custom_tsFieldCO2("tsFieldCO2", "ThingSpeak field pro CO2 (1-8)", defaultTsFieldCO2, 2);
WiFiManagerParameter custom_tsFieldBatt("tsFieldBatt", "ThingSpeak field pro napětí baterie (1-8)", defaultTsFieldBatt, 2);

// Funkce pro validaci konfigurace
void validateConfig() {
  if (config.sleepTime < 1 || config.sleepTime > 200) {
    Serial.println("Neplatná doba spánku, nastavuji na výchozích 5 minut");
    config.sleepTime = atoi(defaultSleepTime);
  }

  if (config.sensorType < SensorModel::SHT40_0X44 || (config.sensorType > SensorModel::DS18B20_OW && config.sensorType != SensorModel::AUTO_DETECT)) {
    Serial.println("Neplatný typ senzoru, nastavuji na auto-detekci");
    config.sensorType = SensorModel::AUTO_DETECT;
  }

  if (config.serverSendType < ServerType::NONE || config.serverSendType > ServerType::BOTH) {
    Serial.println("Neplatný typ odesílání, nastavuji na obě platformy");
    config.serverSendType = ServerType::BOTH;
  }

  if (config.sdaPin < 0 || config.sdaPin > 40) { // ESP32-C3 má méně GPIO, ale pro obecnost
    Serial.println("Neplatný SDA pin, nastavuji na výchozí hodnotu");
    config.sdaPin = DEFAULT_SDA_PIN;
  }

  if (config.sclPin < 0 || config.sclPin > 40) {
    Serial.println("Neplatný SCL pin, nastavuji na výchozí hodnotu");
    config.sclPin = DEFAULT_SCL_PIN;
  }

  // Validace ThingSpeak fieldů (1-8)
  if (config.tsFieldTemp < 1 || config.tsFieldTemp > 8) config.tsFieldTemp = atoi(defaultTsFieldTemp);
  if (config.tsFieldHum < 1 || config.tsFieldHum > 8) config.tsFieldHum = atoi(defaultTsFieldHum);
  if (config.tsFieldPress < 1 || config.tsFieldPress > 8) config.tsFieldPress = atoi(defaultTsFieldPress);
  if (config.tsFieldCO2 < 1 || config.tsFieldCO2 > 8) config.tsFieldCO2 = atoi(defaultTsFieldCO2);
  if (config.tsFieldBatt < 1 || config.tsFieldBatt > 8) config.tsFieldBatt = atoi(defaultTsFieldBatt);
}

// Funkce pro uložení konfigurace do EEPROM
void saveConfig() {
  // Nastavení verze konfigurace
  config.configVersion = CONFIG_VERSION;

  // Uložení celé struktury do EEPROM
  EEPROM.put(0, config);
  EEPROM.commit();

  Serial.println("Konfigurace uložena do EEPROM");
}

// Funkce pro reset konfigurace na výchozí hodnoty a uložení do EEPROM
void resetConfigAndSave() {
  // Resetování konfigurace na výchozí hodnoty přímo ve struktuře config
  strcpy(config.serverAddressTmep, defaultServerAddressTmep);
  strcpy(config.thingSpeakApiKey, defaultThingSpeakApiKey);
  strcpy(config.thingSpeakChannelId, defaultThingSpeakChannelId);
  config.sleepTime = atoi(defaultSleepTime);
  config.sensorType = atoi(defaultSensorType); // AUTO_DETECT
  config.serverSendType = atoi(defaultServerSendType); // BOTH
  config.sdaPin = atoi(defaultSdaPin);
  config.sclPin = atoi(defaultSclPin);
  config.tsFieldTemp = atoi(defaultTsFieldTemp);
  config.tsFieldHum = atoi(defaultTsFieldHum);
  config.tsFieldPress = atoi(defaultTsFieldPress);
  config.tsFieldCO2 = atoi(defaultTsFieldCO2);
  config.tsFieldBatt = atoi(defaultTsFieldBatt);
  config.configVersion = CONFIG_VERSION;

  // Uložení konfigurace do EEPROM
  saveConfig();
  Serial.println("Konfigurace resetována na výchozí hodnoty a uložena.");
}


// Funkce pro načtení konfigurace z EEPROM
bool loadConfig() {
  // Načtení celé struktury z EEPROM
  EEPROM.get(0, config);

  // Kontrola verze konfigurace a platnosti
  if (config.configVersion != CONFIG_VERSION) {
    Serial.println("Neplatná verze konfigurace nebo EEPROM prázdná, nastavuji výchozí hodnoty.");
    resetConfigAndSave(); // Resetuje a uloží výchozí
    return false; // Indikuje, že byly použity výchozí hodnoty
  }

  // Validace načtených hodnot
  validateConfig(); // Validuje a případně opraví hodnoty v 'config'
  // Pokud validace změnila hodnoty, měly by se ideálně znovu uložit.
  // Pro jednoduchost to zde neděláme, uloží se při příštím saveConfigCallback nebo resetu.
  return true;
}


void saveConfigCallback() {
  Serial.println("Ukládání konfigurace...");

  // Uložení parametrů do struktury konfigurace
  strncpy(config.serverAddressTmep, custom_serverAddressTmep.getValue(), sizeof(config.serverAddressTmep) - 1);
  config.serverAddressTmep[sizeof(config.serverAddressTmep) - 1] = '\0'; // Zajistit ukončení null
  strncpy(config.thingSpeakApiKey, custom_thingSpeakApiKey.getValue(), sizeof(config.thingSpeakApiKey) - 1);
  config.thingSpeakApiKey[sizeof(config.thingSpeakApiKey) - 1] = '\0';
  strncpy(config.thingSpeakChannelId, custom_thingSpeakChannelId.getValue(), sizeof(config.thingSpeakChannelId) - 1);
  config.thingSpeakChannelId[sizeof(config.thingSpeakChannelId) - 1] = '\0';

  config.sleepTime = atoi(custom_sleepTime.getValue());
  config.sensorType = atoi(custom_sensorType.getValue());
  config.serverSendType = atoi(custom_serverSendType.getValue());
  config.sdaPin = atoi(custom_sdaPin.getValue());
  config.sclPin = atoi(custom_sclPin.getValue());

  // Uložení ThingSpeak fieldů
  config.tsFieldTemp = atoi(custom_tsFieldTemp.getValue());
  config.tsFieldHum = atoi(custom_tsFieldHum.getValue());
  config.tsFieldPress = atoi(custom_tsFieldPress.getValue());
  config.tsFieldCO2 = atoi(custom_tsFieldCO2.getValue());
  config.tsFieldBatt = atoi(custom_tsFieldBatt.getValue());

  // Validace a uložení konfigurace
  validateConfig();
  saveConfig();
}

// Funkce pro aktualizaci WiFiManager parametrů z aktuální konfigurace
void updateWiFiManagerParametersFromConfig() {
  custom_serverAddressTmep.setValue(config.serverAddressTmep, strlen(config.serverAddressTmep));
  custom_thingSpeakApiKey.setValue(config.thingSpeakApiKey, strlen(config.thingSpeakApiKey));
  custom_thingSpeakChannelId.setValue(config.thingSpeakChannelId, strlen(config.thingSpeakChannelId));
  custom_sleepTime.setValue(String(config.sleepTime).c_str(), String(config.sleepTime).length());
  custom_sensorType.setValue(String(config.sensorType).c_str(), String(config.sensorType).length());
  custom_serverSendType.setValue(String(config.serverSendType).c_str(), String(config.serverSendType).length());
  custom_sdaPin.setValue(String(config.sdaPin).c_str(), String(config.sdaPin).length());
  custom_sclPin.setValue(String(config.sclPin).c_str(), String(config.sclPin).length());
  custom_tsFieldTemp.setValue(String(config.tsFieldTemp).c_str(), String(config.tsFieldTemp).length());
  custom_tsFieldHum.setValue(String(config.tsFieldHum).c_str(), String(config.tsFieldHum).length());
  custom_tsFieldPress.setValue(String(config.tsFieldPress).c_str(), String(config.tsFieldPress).length());
  custom_tsFieldCO2.setValue(String(config.tsFieldCO2).c_str(), String(config.tsFieldCO2).length());
  custom_tsFieldBatt.setValue(String(config.tsFieldBatt).c_str(), String(config.tsFieldBatt).length());
}

// Funkce pro úplný reset nastavení
void fullReset() {
  // Resetování WiFi nastavení
  wm.resetSettings();
  // Resetování konfigurace na výchozí hodnoty
  resetConfigAndSave();
  Serial.println("Všechna nastavení byla resetována na výchozí hodnoty");
}

// Funkce pro nastavení menu WiFiManageru
void setupWiFiManager() {
  // Resetování WiFi nastavení (volitelné, pokud chceme vždy čistý start portálu)
  // wm.resetSettings(); // Již voláno ve fullReset

  // Přidání parametrů do WiFi manageru
  wm.addParameter(&custom_serverAddressTmep);
  wm.addParameter(&custom_thingSpeakApiKey);
  wm.addParameter(&custom_thingSpeakChannelId);
  wm.addParameter(&custom_sleepTime);
  wm.addParameter(&custom_sensorType);
  wm.addParameter(&custom_serverSendType);
  wm.addParameter(&custom_sdaPin);
  wm.addParameter(&custom_sclPin);

  // Přidání parametrů pro ThingSpeak fieldy
  wm.addParameter(&custom_tsFieldTemp);
  wm.addParameter(&custom_tsFieldHum);
  wm.addParameter(&custom_tsFieldPress);
  wm.addParameter(&custom_tsFieldCO2);
  wm.addParameter(&custom_tsFieldBatt);

  // Aktualizace hodnot parametrů z aktuální (pravděpodobně resetované) konfigurace
  updateWiFiManagerParametersFromConfig();

  // Nastavení callback funkce pro uložení konfigurace
  wm.setSaveConfigCallback(saveConfigCallback);

  // Nastavení parametrů na vlastní stránku a oddělení od WiFi stránky
  wm.setParamsPage(true);

  // Nastavení, aby se callback volal i při neúspěšném připojení k WiFi
  wm.setBreakAfterConfig(true);

  // Nastavení vlastního menu s více položkami
  std::vector<const char *> menu = {"wifi","param","info","sep","restart","sep","erase","exit"};
  wm.setMenu(menu);

  // Nastavení tmavého režimu
  wm.setDarkMode(true);

  // Nastavení vlastního titulku
  wm.setTitle("Laskakit Meteo Mini - Konfigurace");

  // Nastavení hostname
  wm.setHostname("LaskakitMeteoMini");

  // Nastavení timeoutu pro konfigurační portál podle typu napájení
  if (isRunningOnBattery()) {
    Serial.println("Napájení z baterie - nastavuji kratší timeout portálu");
    wm.setConfigPortalTimeout(CONFIG_TIMEOUT_BATTERY);
  } else {
    Serial.println("Externí napájení - nastavuji delší timeout portálu");
    wm.setConfigPortalTimeout(CONFIG_TIMEOUT_EXTERNAL);
  }

  // Nastavení zobrazení tlačítek na informační stránce
  wm.setShowInfoErase(true);

  // Nastavení vlastního HTML kódu na konec stránky
  char footerBuffer[60];
  snprintf(footerBuffer, sizeof(footerBuffer), "<p>Firmware verze %s</p>", FIRMWARE_VERSION);
  wm.setCustomBodyFooter(footerBuffer);

  // Nastavení zobrazení hesla
  wm.setShowPassword(true);

  // Nastavení HTTP portu
  wm.setHttpPort(80);

  // Nastavení WiFi kanálu pro AP
  wm.setWiFiAPChannel(1);
}

// Funkce pro vstup do konfiguračního režimu
void enterConfigurationMode(const char* apName) {
  Serial.println("Vstupuji do konfiguračního režimu...");

  // Inicializace EEPROM před resetováním nastavení (pro jistotu)
  EEPROM.begin(EEPROM_SIZE);
  fullReset(); // Resetuje WiFi a vlastní konfiguraci na výchozí hodnoty

  // Nastavení WiFiManageru
  setupWiFiManager(); // Nastaví parametry a jejich výchozí hodnoty pro zobrazení

  // Spuštění konfiguračního portálu
  if (!wm.startConfigPortal(apName)) {
    Serial.println("Nepodařilo se spustit konfigurační portál nebo vypršel timeout");
    portalRunning = false;
    if (isRunningOnBattery()) {
      Serial.println("Napájení z baterie - přecházím do režimu spánku po neúspěšném portálu");
      esp_sleep_enable_timer_wakeup(config.sleepTime * 60 * 1000000ULL);
      esp_deep_sleep_start();
    }
  } else {
    Serial.println("Připojeno k WiFi přes konfigurační portál nebo konfigurace uložena.");
    portalRunning = true; // portalRunning se nastaví na true, aby wm.process() byl volán
  }
}

// Funkce pro kontrolu OnDemand konfigurace
void checkButton() {
  // Pokud je portál spuštěn, zpracovat požadavky
  if (portalRunning) {
    wm.process();
  }

  // Kontrola, zda je tlačítko stisknuto
  if (digitalRead(TRIGGER_PIN) == LOW) {
    delay(50);  // Debounce
    if (digitalRead(TRIGGER_PIN) == LOW) {
      if (!portalRunning) {
        Serial.println("Tlačítko stisknuto, spouštím konfigurační portál");
        enterConfigurationMode("Laskakit Meteo Mini Config");
      } else {
        Serial.println("Tlačítko stisknuto, zastavuji konfigurační portál");
        wm.stopWebPortal();
        portalRunning = false;
       }
    }
  }
}

// Funkce pro odeslání dat na ThingSpeak
bool sendToThingSpeak(float temperature, float humidity, float pressure, uint16_t co2, float batteryVoltage) {
  // Kontrola, zda jsou vyplněny povinné údaje
  if (strlen(config.thingSpeakApiKey) == 0 || strlen(config.thingSpeakChannelId) == 0) {
    Serial.println("Chybí ThingSpeak API klíč nebo Channel ID");
    return false;
  }

  HTTPClient http;
  char url[64]; // Buffer pro URL
  snprintf(url, sizeof(url), "http://api.thingspeak.com/update");

  // Dynamické vytvoření POST requestu podle konfigurace
  char postData[256]; // Buffer pro POST data
  snprintf(postData, sizeof(postData), "api_key=%s", config.thingSpeakApiKey);

  char paramBuffer[64]; // Pomocný buffer pro jednotlivé parametry

  snprintf(paramBuffer, sizeof(paramBuffer), "&field%d=%.2f", config.tsFieldTemp, temperature);
  strncat(postData, paramBuffer, sizeof(postData) - strlen(postData) - 1);

  if (config.sensorType == SensorModel::SHT40_0X44 || config.sensorType == SensorModel::SHT40_0X45 ||
      config.sensorType == SensorModel::BME280_0X76 || config.sensorType == SensorModel::BME280_0X77 ||
      config.sensorType == SensorModel::SCD41_0X62) {
    snprintf(paramBuffer, sizeof(paramBuffer), "&field%d=%.2f", config.tsFieldHum, humidity);
    strncat(postData, paramBuffer, sizeof(postData) - strlen(postData) - 1);
  }

  if (config.sensorType == SensorModel::BME280_0X76 || config.sensorType == SensorModel::BME280_0X77) {
    snprintf(paramBuffer, sizeof(paramBuffer), "&field%d=%.2f", config.tsFieldPress, pressure);
    strncat(postData, paramBuffer, sizeof(postData) - strlen(postData) - 1);
  }

  if (config.sensorType == SensorModel::SCD41_0X62) {
    snprintf(paramBuffer, sizeof(paramBuffer), "&field%d=%u", config.tsFieldCO2, co2);
    strncat(postData, paramBuffer, sizeof(postData) - strlen(postData) - 1);
  }

  snprintf(paramBuffer, sizeof(paramBuffer), "&field%d=%.2f", config.tsFieldBatt, batteryVoltage);
  strncat(postData, paramBuffer, sizeof(postData) - strlen(postData) - 1);

  http.begin(url);
  http.addHeader("Content-Type", "application/x-www-form-urlencoded");

  int httpResponseCode = http.POST(postData);
  http.end();

  Serial.print("ThingSpeak HTTP kód: ");
  Serial.println(httpResponseCode);

  return (httpResponseCode > 0);
}

void setup() {
  // Inicializace sériové komunikace
  Serial.begin(115200);
  delay(100);

  // Inkrementace počtu bootů
  bootCount++;

  // Výpis důvodu probuzení
  esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();

  Serial.println("\n Starting");
  Serial.printf("Boot count: %d\n", bootCount);

  // Výpis důvodu probuzení
  switch(wakeup_reason) {
    case ESP_SLEEP_WAKEUP_TIMER:
      Serial.println("Probuzení způsobeno časovačem");
      break;
    case ESP_SLEEP_WAKEUP_GPIO:
      Serial.println("Probuzení způsobeno tlačítkem (GPIO)");
      break;
    default:
      Serial.printf("Probuzení nebylo způsobeno deep sleep: %d\n", wakeup_reason);
      break;
  }

  // Kontrola stavu vstupního pinu tlačítka při startu
  pinMode(TRIGGER_PIN, INPUT_PULLUP);
  delay(100); // Přidáno zpoždění pro stabilizaci po probuzení z deep sleep

  // Kontrola, zda bylo probuzení způsobeno tlačítkem nebo je tlačítko stisknuto při startu
  if (wakeup_reason == ESP_SLEEP_WAKEUP_GPIO || digitalRead(TRIGGER_PIN) == LOW) {
    Serial.println("Tlačítko je stisknuto nebo probuzení bylo způsobeno tlačítkem. Vstupuji do konfiguračního režimu.");
    delay(500); // Delší zpoždění pro stabilizaci systému před spuštěním WiFiManager
    WiFi.mode(WIFI_STA);
    enterConfigurationMode("Laskakit Meteo Mini Config");
    // Po enterConfigurationMode() může být portalRunning true, nebo program usne
  }

  // Inicializace EEPROM a načtení uložené konfigurace
  EEPROM.begin(EEPROM_SIZE);

  // Nastavení pinů
  pinMode(PWR_PIN, OUTPUT);
  digitalWrite(PWR_PIN, HIGH);  // Zapnutí napájení pro senzory

  Serial.println("-------------------");
  Serial.println("Laskakit Meteo Mini");
  Serial.println("TMEP a ThingSpeak firmware");
  Serial.println("Podpora sezorů SHT40 (0x44, 0x45), BME280 (0x76, 0x77), SCD41, DS18B20");
  Serial.println("!!POZOR!! připojit pouze jeden senzor !!POZOR!!");
  Serial.print("verze: "); Serial.println(FIRMWARE_VERSION);
  Serial.println("-------------------");

  // Načtení konfigurace z EEPROM
  if (loadConfig()) {
    Serial.println("Konfigurace úspěšně načtena z EEPROM");
  } else {
    Serial.println("Načtení konfigurace selhalo, použity/uloženy výchozí hodnoty");
  }

  Serial.println("Uložená adresa TMEP serveru: " + String(config.serverAddressTmep));
  Serial.println("Uložený ThingSpeak Channel ID: " + String(config.thingSpeakChannelId));
  Serial.println("Doba spánku: " + String(config.sleepTime) + " minut");
  Serial.println("Typ senzoru: " + String(config.sensorType));
  Serial.println("Odesílání dat: " + String(config.serverSendType));
  Serial.println("I2C SDA pin: " + String(config.sdaPin));
  Serial.println("I2C SCL pin: " + String(config.sclPin));

  // Aktualizace parametrů WiFiManageru, aby odpovídaly načtené/resetované konfiguraci
  // Toto je důležité, pokud by se portál spouštěl později bez fullReset
  updateWiFiManagerParametersFromConfig();

  // Nastavení callback funkce pro uložení konfigurace (pro případ, že by se portál spouštěl přes checkButton bez setupWiFiManager)
  wm.setSaveConfigCallback(saveConfigCallback);
  wm.setParamsPage(true);
  wm.setBreakAfterConfig(true);


  // Připojení k WiFi, pokud jsme ještě nepřipojeni a portál neběží
  if (WiFi.status() != WL_CONNECTED && !portalRunning) {
    WiFi.mode(WIFI_STA);
    wm.setConnectTimeout(20); // 20 sekund na připojení

    Serial.println("Pokus o připojení k WiFi...");
    if (!wm.autoConnect("Laskakit Meteo Mini Config")) { // AP se spustí, pokud se nepřipojí
      Serial.println("Nepodařilo se připojit k WiFi přes autoConnect. Možná běží AP.");
      // Pokud autoConnect spustil AP, portalRunning by mělo být true.
      // Pokud ne, a jsme na baterii, usnout.
      if (!wm.getConfigPortalActive() && isRunningOnBattery()) {
          Serial.println("AP neběží a jsme na baterii, usínám.");
          esp_sleep_enable_timer_wakeup(config.sleepTime * 60 * 1000000ULL);
          esp_deep_sleep_start();
      } else if (wm.getConfigPortalActive()) {
          portalRunning = true; // Ujistíme se, že je nastaveno
      }
    } else {
        Serial.println("Připojeno k WiFi přes autoConnect.");
    }
  } else if (WiFi.status() == WL_CONNECTED) {
     Serial.println("Již připojeno k WiFi.");
  }


  // Inicializace I2C s nastavenými piny
  Wire.begin(config.sdaPin, config.sclPin); // Použití nakonfigurovaných pinů

  // Inicializace OneWire
  DS18B20.begin(); // 10 - DATA

  // Inicializace a detekce senzorů
  uint16_t error_sens; // Přejmenováno, aby nekolidovalo s globální proměnnou error
  char errorMessage[256];
  bool sensorFound = false;

  /* SHT40 - 0x44 */
  if((config.sensorType == SensorModel::SHT40_0X44) || (config.sensorType == SensorModel::AUTO_DETECT))
  {
    sht4x.begin(Wire, 0x44);
    uint32_t serialNumber;
    error_sens = sht4x.serialNumber(serialNumber);
    if (error_sens) {
      Serial.println("Nelze najít platný snímač SHT40 (0x44), zkontrolujte zapojení!");
    } else {
      Serial.println("Senzor SHT40 (0x44) nalezen");
      if (config.sensorType == SensorModel::AUTO_DETECT) config.sensorType = SensorModel::SHT40_0X44;
      sensorFound = true;
    }
  }

  if (!sensorFound && ((config.sensorType == SensorModel::SHT40_0X45) || (config.sensorType == SensorModel::AUTO_DETECT)))
  {
    sht4x.begin(Wire, 0x45);
    uint32_t serialNumber;
    error_sens = sht4x.serialNumber(serialNumber);
    if (error_sens) {
      Serial.println("Nelze najít platný snímač SHT40 (0x45), zkontrolujte zapojení!");
    } else {
      Serial.println("Senzor SHT40 (0x45) nalezen");
      if (config.sensorType == SensorModel::AUTO_DETECT) config.sensorType = SensorModel::SHT40_0X45;
      sensorFound = true;
    }
  }

  if (!sensorFound && ((config.sensorType == SensorModel::BME280_0X76) || (config.sensorType == SensorModel::AUTO_DETECT)))
  {
    if (!bme.begin(0x76)) {
      Serial.println("Nelze najít platný snímač BME280 (0x76), zkontrolujte zapojení!");
    } else {
      Serial.println("Senzor BME280 (0x76) nalezen");
      if (config.sensorType == SensorModel::AUTO_DETECT) config.sensorType = SensorModel::BME280_0X76;
      sensorFound = true;
      bme.setSampling(Adafruit_BME280::MODE_FORCED, Adafruit_BME280::SAMPLING_X1, Adafruit_BME280::SAMPLING_X1, Adafruit_BME280::SAMPLING_X1, Adafruit_BME280::FILTER_OFF);
    }
  }

  if (!sensorFound && ((config.sensorType == SensorModel::BME280_0X77) || (config.sensorType == SensorModel::AUTO_DETECT)))
  {
    if (!bme.begin(0x77)) {
      Serial.println("Nelze najít platný snímač BME280 (0x77), zkontrolujte zapojení!");
    } else {
      Serial.println("Senzor BME280 (0x77) nalezen");
      if (config.sensorType == SensorModel::AUTO_DETECT) config.sensorType = SensorModel::BME280_0X77;
      sensorFound = true;
      bme.setSampling(Adafruit_BME280::MODE_FORCED, Adafruit_BME280::SAMPLING_X1, Adafruit_BME280::SAMPLING_X1, Adafruit_BME280::SAMPLING_X1, Adafruit_BME280::FILTER_OFF);
    }
  }

  if (!sensorFound && ((config.sensorType == SensorModel::SCD41_0X62) || (config.sensorType == SensorModel::AUTO_DETECT)))
  {
    SCD4X.begin(Wire, 0x62);
    error_sens = SCD4X.startPeriodicMeasurement();
    if (error_sens) {
      Serial.println("Nelze najít platný snímač SCD41, zkontrolujte zapojení!");
    } else {
      Serial.println("Senzor SCD41 nalezen");
      if (config.sensorType == SensorModel::AUTO_DETECT) config.sensorType = SensorModel::SCD41_0X62;
      sensorFound = true;
      delay(5000); // Čas na zahřátí SCD41
    }
  }

  if (!sensorFound && ((config.sensorType == SensorModel::DS18B20_OW) || (config.sensorType == SensorModel::AUTO_DETECT)))
  {
    if (!oneWire.search(sensorAddress)) {
      Serial.println("Nelze najít platný snímač DS18B20, zkontrolujte zapojení!");
      oneWire.reset_search();
    } else {
      Serial.println("Senzor DS18B20 nalezen");
      if (config.sensorType == SensorModel::AUTO_DETECT) config.sensorType = SensorModel::DS18B20_OW;
      sensorFound = true;
    }
  }

  if (!sensorFound && config.sensorType == SensorModel::AUTO_DETECT) {
    Serial.println("VAROVÁNÍ: Nebyl nalezen žádný senzor při auto-detekci!");
  } else if (!sensorFound) {
    Serial.println("VAROVÁNÍ: Vynucený typ senzoru nebyl nalezen!");
  }
}

void loop() {
  // Kontrola tlačítka pro OnDemand konfiguraci
  checkButton();

  // Pokud běží konfigurační portál, neprovádět měření
  if (portalRunning) {
    delay(100);   // Malé zpoždění pro optimalizaci
    return;
  }

  // Kontrola, zda je WiFi stále připojeno, pokud ne, pokus o opětovné připojení
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi spojení ztraceno, pokus o znovupřipojení...");
    WiFi.reconnect();
    int reconnectAttempts = 0;
    while (WiFi.status() != WL_CONNECTED && reconnectAttempts < 20) {
      delay(500);
      Serial.print(".");
      reconnectAttempts++;
    }
    Serial.println();

    if (WiFi.status() != WL_CONNECTED) {
      Serial.println("Nepodařilo se znovu připojit k WiFi");
    } else {
      Serial.println("WiFi znovu připojeno");
    }
  }

  // Měření dat
  float temperature = NAN; // Inicializace na Not-a-Number
  float humidity = NAN;
  float pressure = NAN;
  uint16_t co2 = 0; // CO2 nemůže být NAN, 0 je rozumná výchozí hodnota pro chybu
  float battery_voltage = 0;

  battery_voltage = getBatteryVoltage();
  delay(100);

  bool sensorReadSuccess = false;
  uint16_t error_read; // Přejmenováno
  char errorMessage[256];

  if (config.sensorType == SensorModel::SHT40_0X44 || config.sensorType == SensorModel::SHT40_0X45)
  {
    error_read = sht4x.measureHighPrecision(temperature, humidity);
    if (error_read) {
      Serial.print("Error trying to execute measureHighPrecision(): ");
      errorToString(error_read, errorMessage, 256);
      Serial.println(errorMessage);
    } else if (isnan(temperature) || temperature < -40 || temperature > 85 || isnan(humidity) || humidity < 0 || humidity > 100) {
      Serial.println("Neplatné hodnoty ze senzoru SHT40");
    } else {
      sensorReadSuccess = true;
    }
  }
  else if (config.sensorType == SensorModel::BME280_0X76 || config.sensorType == SensorModel::BME280_0X77)
  {
    bme.takeForcedMeasurement();
    temperature = bme.readTemperature();
    humidity = bme.readHumidity();
    pressure = bme.readPressure() / 100.0F;
    if (isnan(temperature) || isnan(humidity) || isnan(pressure) ||
        temperature < -40 || temperature > 85 ||
        humidity < 0 || humidity > 100 ||
        pressure < 300 || pressure > 1100) {
      Serial.println("Chyba při čtení dat z BME280 senzoru nebo neplatné hodnoty");
    } else {
      sensorReadSuccess = true;
    }
  }
  else if (config.sensorType == SensorModel::SCD41_0X62)
  {
    delay(5000); // SCD41 potřebuje čas
    error_read = SCD4X.readMeasurement(co2, temperature, humidity);
    if (error_read) {
      Serial.print("Error trying to execute readMeasurement(): ");
      errorToString(error_read, errorMessage, 256);
      Serial.println(errorMessage);
    } else if (co2 == 0 || co2 > 10000 || isnan(temperature) || isnan(humidity)) { // 0 může být chyba, 10000 je horní limit senzoru
      Serial.println("Neplatné hodnoty ze senzoru SCD41");
    } else {
      sensorReadSuccess = true;
    }
  }
  else if(config.sensorType == SensorModel::DS18B20_OW)
  {
    DS18B20.requestTemperatures();
    temperature = DS18B20.getTempC(sensorAddress);
    if (temperature == DEVICE_DISCONNECTED_C || isnan(temperature) || temperature < -55 || temperature > 125) { // Rozsah DS18B20
      Serial.println("Chyba při čtení teploty z DS18B20 senzoru nebo neplatná hodnota");
    } else {
      sensorReadSuccess = true;
    }
  }

  /* Print to Serial Monitor */
  Serial.print("Temperature: "); Serial.print(temperature, 2); Serial.println(" °C");
  if (config.sensorType != SensorModel::DS18B20_OW) { // DS18B20 nemá vlhkost
    Serial.print("Humidity: "); Serial.print(humidity, 2); Serial.println(" %");
  }
  if (config.sensorType == SensorModel::BME280_0X76 || config.sensorType == SensorModel::BME280_0X77) {
    Serial.print("Pressure: "); Serial.print(pressure, 2); Serial.println(" hPa");
  }
  if (config.sensorType == SensorModel::SCD41_0X62) {
    Serial.print("CO2: "); Serial.print(co2); Serial.println(" ppm");
  }
  Serial.print("Battery voltage: "); Serial.print(battery_voltage, 2); Serial.println(" V");

  // Kontrola platnosti naměřených hodnot (již částečně provedena výše)
  bool validData = sensorReadSuccess; // Pokud čtení selhalo, data nejsou validní
  if (isnan(battery_voltage) || battery_voltage < 0 || battery_voltage > 5) { // Kontrola baterie
    Serial.println("Neplatná hodnota napětí baterie: " + String(battery_voltage, 2));
    validData = false;
  }


  // Otestování, zda je WiFi připojeno a odeslání dat
  bool sendSuccess = false;
  if (WiFi.status() == WL_CONNECTED && validData) {
    // Odeslání na TMEP.CZ
    if ((config.serverSendType == ServerType::TMEP || config.serverSendType == ServerType::BOTH) && strlen(config.serverAddressTmep) > 0) {
      HTTPClient http;
      char urlBuffer[256];
      char paramBuffer[64];

      snprintf(urlBuffer, sizeof(urlBuffer), "http://%s/?temp=%.2f&rssi=%d",
               config.serverAddressTmep, temperature, WiFi.RSSI());

      if (config.sensorType != SensorModel::DS18B20_OW) {
        snprintf(paramBuffer, sizeof(paramBuffer), "&humV=%.2f", humidity);
        strncat(urlBuffer, paramBuffer, sizeof(urlBuffer) - strlen(urlBuffer) - 1);
      }
      if (config.sensorType == SensorModel::BME280_0X76 || config.sensorType == SensorModel::BME280_0X77) {
        snprintf(paramBuffer, sizeof(paramBuffer), "&pressV=%.2f", pressure);
        strncat(urlBuffer, paramBuffer, sizeof(urlBuffer) - strlen(urlBuffer) - 1);
      }
      if (config.sensorType == SensorModel::SCD41_0X62) {
        snprintf(paramBuffer, sizeof(paramBuffer), "&CO2=%u", co2);
        strncat(urlBuffer, paramBuffer, sizeof(urlBuffer) - strlen(urlBuffer) - 1);
      }
      snprintf(paramBuffer, sizeof(paramBuffer), "&voltage=%.2f", battery_voltage);
      strncat(urlBuffer, paramBuffer, sizeof(urlBuffer) - strlen(urlBuffer) - 1);

      http.begin(urlBuffer);
      Serial.println(urlBuffer);
      int httpResponseCode = http.GET();
      http.end();

      if (httpResponseCode > 0) {
        Serial.println("TMEP HTTP kód: " + String(httpResponseCode));
        sendSuccess = true;
      } else {
        Serial.println("Chyba v HTTP požadavku na TMEP");
      }
    }

    // Odeslání na ThingSpeak
    if (config.serverSendType == ServerType::THINGSPEAK || config.serverSendType == ServerType::BOTH) {
      bool thingSpeakResult = sendToThingSpeak(temperature, humidity, pressure, co2, battery_voltage);
      sendSuccess |= thingSpeakResult; // Pokud TMEP uspěl, sendSuccess zůstane true
    }

    if (!sendSuccess && (config.serverSendType != ServerType::NONE)) { // Pokud se mělo odesílat a neodeslalo
      Serial.println("Nepodařilo se odeslat data na žádný server");
    }
  } else if (!validData) {
    Serial.println("Neplatná data ze senzorů, neodesílám.");
  } else {
    Serial.println("WiFi není připojeno, neodesílám.");
  }

  Serial.print("Meteo Mini přechází do spánku na ");
  Serial.print(config.sleepTime);
  Serial.println(" minut");

  digitalWrite(PWR_PIN, LOW); // Vypnutí napájení senzorů
  Serial.flush();
  delay(100);

  // Kontrola, zda bylo provedeno úspěšné odeslání, pokud ne, zkusíme to za kratší dobu
  if (!sendSuccess && WiFi.status() == WL_CONNECTED && (config.serverSendType != ServerType::NONE)) {
    Serial.println("Nepodařilo se odeslat data (ale WiFi je OK), zkusíme to znovu za 5 minut");
    esp_sleep_enable_timer_wakeup(5 * 60 * 1000000ULL);
  } else if (!sendSuccess && (config.serverSendType != ServerType::NONE)) {
    Serial.println("Nepodařilo se odeslat data (WiFi není připojeno), zkusíme to znovu za 10 minut");
    esp_sleep_enable_timer_wakeup(10 * 60 * 1000000ULL);
  } else {
    esp_sleep_enable_timer_wakeup(config.sleepTime * 60 * 1000000ULL);
  }

  pinMode(TRIGGER_PIN, INPUT_PULLUP); // Ujistit se, že je pin nastaven pro probuzení
  esp_deep_sleep_enable_gpio_wakeup(1ULL << TRIGGER_PIN, ESP_GPIO_WAKEUP_GPIO_LOW);
  esp_deep_sleep_start();
}
