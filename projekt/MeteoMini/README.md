# MeteoMini v2.5. - Profesionální IoT Meteostanice

## Popis projektu

Tento projekt transformuje kitu Laskakit MeteoMini (ESP32-C3) na pokročilou a robustní IoT meteorologickou stanici. Firmware je navržen s důrazem na nízkou spotřebu, flexibilitu a snadnou konfigurovatelnost.

## Klíčové vlastnosti

- **Podpora více senzorů:** Automatická detekce nebo manuální výběr senzorů SHT40, BME280, SCD41 a DS18B20.
- **Podpora více serverů:** Možnost odesílat data současně na TMEP.cz, ThingSpeak a libovolný MQTT broker.
- **Zabezpečené MQTT:** Komunikace s MQTT brokerem je šifrovaná (TLS/SSL), což umožňuje bezpečné připojení např. do domá<PERSON>í<PERSON> systému Home Assistant přes veřejný internet.
- **Pokročilá správa napájení:** Využití hlubokého spánku (Deep Sleep) pro minimalizaci spotřeby a nouzový režim při nízkém stavu baterie.
- **Ukládání dat offline:** Pokud není dostupné Wi-Fi připojení, zařízení uloží poslední měření do RTC paměti a odešle ho po obnovení spojení.
- **Webový konfigurační portál:** Po stisku tlačítka zařízení vytvoří Wi-Fi AP, přes kterou lze v přehledném webovém rozhraní nastavit veškeré parametry bez nutnosti měnit kód.
- **Modulární struktura kódu:** Kód je rozdělen do logických celků (`.h` souborů) pro snadnou orientaci a budoucí rozšiřování.

## Hardware

- **Základní deska:** [LaskaKit ESP32-C3 Meteo Mini](https://laskakit.cz/laskakit-esp32-c3-meteo-mini/)
- **Schéma zapojení:** [PDF Dokumentace k HW](https://github.com/LaskaKit/Meteo_Mini/blob/main/HW/LaskaKit_METEO_MINI_v_3_5.pdf)
- **Senzory:** Jeden z podporovaných (SHT40, BME280, SCD41, DS18B20)
- **Napájení:** Li-Po/Li-Ion baterie nebo externí 5V zdroj

### Zapojení pinů

- `LED_PIN`: 7 (Stavová LED)
- `TRIGGER_PIN`: 2 (Tlačítko pro spuštění konfigurace)
- `PWR_PIN`: 3 (Napájení senzorů)
- `ONEWIRE_PIN`: 10 (Sběrnice pro DS18B20)
- `DEFAULT_SDA_PIN`: 19 (I2C SDA)
- `DEFAULT_SCL_PIN`: 18 (I2C SCL)

## Potřebné knihovny

Pro úspěšnou kompilaci v Arduino IDE je nutné nainstalovat následující knihovny přes "Správce knihoven". Jsou uvedeny i verze, se kterými byl projekt testován, pro zajištění budoucí kompatibility.

- [WiFiManager by tzapu](https://github.com/tzapu/WiFiManager) (verze 2.0.17)
- [PubSubClient by Nick O'Leary](https://github.com/knolleary/pubsubclient) (verze 2.8)
- [Sensirion I2C SHT4x by Sensirion](https://github.com/Sensirion/arduino-i2c-sht4x) (verze 1.1.2)
- [Adafruit BME280 Library by Adafruit](https://github.com/adafruit/Adafruit_BME280_Library) (verze 2.2.4)
- [DallasTemperature by Miles Burton](https://github.com/milesburton/Arduino-Temperature-Control-Library) (verze 4.0.3)
- [OneWire by Paul Stoffregen](https://github.com/PaulStoffregen/OneWire) (verze 2.3.8)
- [SCD4X by Sensirion](https://github.com/Sensirion/arduino-i2c-scd4x) (verze 1.1.0)
_ [EEPROM by Arduino](https://github.com/arduino-libraries/EEPROM) (verze 2.0.0)
- [ArduinoJson by Benoit Blanchon](https://github.com/bblanchon/ArduinoJson) (verze 6.21.2)
- [ArduinoOTA by Arduino](https://github.com/esp8266/Arduino/tree/master/libraries/ArduinoOTA) (verze 1.0.0)
- [esp32-c3 by Espressif Systems](https://github.com/espressif/arduino-esp32/tree/master/libraries/WiFi) (verze 3.3.0)
- [esp32-c3 by Espressif Systems](https://github.com/espressif/arduino-esp32/tree/master/libraries/HTTPClient) (verze 3.3.0)  
- [esp32-c3 by Espressif Systems](https://github.com/espressif/arduino-esp32/tree/master/libraries/Update) (verze 3.3.0)  
- [esp32-c3 by Espressif Systems](https://github.com/espressif/arduino-esp32/tree/master/libraries/HTTPUpdate) (verze 3.3.0)
- [esp32-c3 by Espressif Systems](https://github.com/espressif/arduino-esp32/tree/master/libraries/ESPmDNS) (verze 3.3.0)
- [esp32-c3 by Espressif Systems](https://github.com/espressif/arduino-esp32/tree/master/libraries/ESPmDNS) (verze 3.3.0) 



## Struktura kódu

Projekt je rozdělen do následujících souborů:

- `verze_1.ino`: Hlavní soubor, obsahuje funkce `setup()` a `loop()`, řídí celkovou logiku.
- `Config.h`: Definuje všechny konfigurační konstanty a strukturu `Config` pro ukládání nastavení v EEPROM.
- `Power.h`: Funkce pro správu napájení, měření baterie a hluboký spánek.
- `LED.h`: Funkce pro ovládání stavové LED.
- `Sensors.h`: Všechny funkce pro inicializaci a čtení dat ze senzorů.
- `Network.h`: Nejobsáhlejší soubor, obsahuje logiku pro Wi-Fi, konfigurační portál a odesílání dat na všechny podporované servery (TMEP, ThingSpeak, MQTT).

## Konfigurace zařízení

1.  Po prvním spuštění (nebo po stisku tlačítka `TRIGGER` na déle než 1 sekundu) se zařízení přepne do konfiguračního režimu.
2.  LED dioda začne blikat a zařízení vytvoří Wi-Fi síť s názvem **"Laskakit Meteo Mini Config"**.
3.  Připojte se k této Wi-Fi síti pomocí telefonu nebo počítače.
4.  Po připojení se automaticky otevře webový portál (pokud ne, zadejte do prohlížeče adresu `***********`).
5.  V portálu nejprve vyberte vaši domácí Wi-Fi a zadejte heslo.
6.  Poté v sekci "Parameters" nastavte vše potřebné:

### Popis nastavení

- **Základní nastavení:**
  - `Doba spánku`: Interval měření v minutách.
  - `Typ senzoru`: Zadejte `99` pro automatickou detekci (doporučeno), nebo číslo konkrétního senzoru.
  - `I2C SDA/SCL pin`: Ponechte výchozí, pokud nemáte speciální důvod je měnit.

- **Kam odesílat data?:**
  - Pomocí zaškrtávacích políček vyberte libovolnou kombinaci serverů, na které chcete data posílat.

- **TMEP.cz:**
  - `TMEP.CZ adresa`: Vaše unikátní adresa ze stránek TMEP.cz.

- **ThingSpeak:**
  - `ThingSpeak API Key`: "Write API Key" z vašeho kanálu.
  - `ThingSpeak Channel ID`: ID vašeho kanálu.
  - `TS pole - ...`: Čísla polí (fields) pro jednotlivé veličiny ve vašem kanálu.

- **MQTT:**
  - `MQTT Server`: Adresa vašeho MQTT brokeru. Pro lokální síť zadejte IP adresu (např. `*************`), pro veřejný přístup vaši DuckDNS adresu (např. `jmeno.duckdns.org`).
  - `MQTT Port`: Port vašeho brokeru. Pro nezabezpečené připojení `1883`, pro **zabezpečené (doporučeno)** `8883`.
  - `MQTT Uživatel / Heslo`: Přihlašovací údaje k vašemu brokeru.
  - `MQTT Téma (topic)`: Téma, na které se mají data publikovat (např. `meteomini/data`).

Po uložení se zařízení restartuje a začne fungovat v normálním režimu.

## Nastavení MQTT s Home Assistant (přes DuckDNS)

Pro bezpečné připojení z veřejné sítě do vaší lokální sítě je potřeba:

1.  **Mít funkční DuckDNS a SSL certifikát:** V Home Assistant se o to stará doplněk "DuckDNS".
2.  **Nakonfigurovat MQTT broker (Mosquitto) pro bezpečné připojení:** V nastavení doplňku Mosquitto je třeba zapnout SSL a nastavit cesty k certifikátům, které spravuje DuckDNS.
3.  **Nastavit přesměrování portů (Port Forwarding):** Na vašem domácím routeru je nutné nastavit pravidlo, které bude přesměrovávat příchozí provoz z internetu na portu `8883` na lokální IP adresu vašeho Home Assistant na port `8883`.

