/*
*  Firmware pro Meteo Mini ESP32-C3 s podporou TMEP.CZ a ThingSpeak
*  Umožňuje flexibilní konfiguraci odesíl<PERSON>í dat
*  Obsahuje OnDemand konfiguraci pomocí tlačítka
*  Firmware vyhledá adresy následujíc<PERSON>ch senzorů:
*
*  - SHT40 (0x44)
*  - SHT40 (0x45)
*  - BME280 (0x76)
*  - BME280 (0x77)
*  - SCD41
*  - DS18B20
*
*  Případně lze typ senzoru vynutit, čí<PERSON>ž se automatické vyhledávání zakáže.
*
*  Podle nalezených senzorů se vyplní GET URL:
*  - SHT40: teplota + vlhkost + napětí baterie
*  - BME280: teplota + vlhkost + tlak + napětí baterie
*  - SCD41: teplota + vlhkost + CO2 + napětí baterie
*  - DS18B20: teplota + napětí baterie
*
*  POZOR: Připojte současně POUZE JEDEN senzor!!!
*/

#include <Arduino.h>
#include <SensirionI2cSht4x.h>
#include <Adafruit_BME280.h>
#include <SensirionI2cScd4x.h>
#include <Wire.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <WiFiManager.h> // https://github.com/tzapu/WiFiManager
#include <DNSServer.h>
#include <WebServer.h>
#include <EEPROM.h>
#include <OneWire.h>
#include <DallasTemperature.h>
#include <esp_sleep.h>
#include <driver/gpio.h>

// Ukládání hodnoty do RTC paměti (přetrvává během deep sleep)
RTC_DATA_ATTR int bootCount = 0;

#define version 2.3  // Verze firmware - zvýšena kvůli opravě

// Konfigurace pinů
#define ONE_WIRE_BUS 10 // Pin pro připojení DS18B20 senzoru
#define DIVIDER_RATIO 1.7693877551 // Poměr děliče napětí pro měření baterie
#define PWR_PIN 3 // Pin pro zapínání/vypínání napájení uSup
#define ADC_IN 0 // Analogový vstup pro měření napětí baterie
#define TRIGGER_PIN 2  // Pin pro zapnutí konfiguračního portálu
// Bitová maska pro probuzení z deep sleep je definována přímo v kódu jako 1ULL << TRIGGER_PIN
#define BATTERY_THRESHOLD 3.8 // Prahová hodnota napětí pro detekci napájení z baterie (V)
#define CONFIG_TIMEOUT_BATTERY 60 // Timeout konfiguračního portálu při napájení z baterie (sekundy)
#define CONFIG_TIMEOUT_EXTERNAL 120 // Timeout konfiguračního portálu při externím napájení (sekundy)

// Výchozí hodnoty pro I2C piny
// Konfigurace I2C pinů - nyní nastavitelné v EEPROM
#define DEFAULT_SDA_PIN 19  //
#define DEFAULT_SCL_PIN 18  //

// Definice verze konfigurace
#define CONFIG_VERSION 2  // Zvýšit při změně struktury konfigurace

// Definice adres v EEPROM
#define EEPROM_SIZE 512

// Výčet druhů serverů pro odesílání dat
enum ServerType {
  NONE = 0,
  TMEP = 1,
  THINGSPEAK = 2,
  BOTH = 3
};

// Struktura pro konfiguraci
struct Config {
  char serverAddressTmep[40];
  char thingSpeakApiKey[40];
  char thingSpeakChannelId[20];
  int sleepTime;
  int sensorType;
  int serverSendType;
  int sdaPin;
  int sclPin;
  int tsFieldTemp;
  int tsFieldHum;
  int tsFieldPress;
  int tsFieldCO2;
  int tsFieldBatt;
  uint8_t configVersion;
  uint32_t checksum; // Přidán checksum pro kontrolu integrity dat
};

// Globální instance konfigurace
Config config;

// Globální proměnné pro zpětnou kompatibilitu
char serverAddressTmep[40] = "xny3ef-8khshy.tmep.cz/index.php";
char thingSpeakApiKey[40] = "ABS8T4OULVYA1FOL";     // apiKey pro zápis
char thingSpeakChannelId[20] = "845449";  // Číslo kanálu
int sleepTime = 5;
int sensorType = 99;
int serverSendType = ServerType::BOTH;  // Výchozí nastavení - odesílat na obě platformy
int sdaPin = DEFAULT_SDA_PIN;  // Výchozí SDA pin pro I2C
int sclPin = DEFAULT_SCL_PIN;  // Výchozí SCL pin pro I2C

// Konfigurace ThingSpeak polí
int tsFieldTemp = 2;      // Výchozí field pro teplotu
int tsFieldHum = 3;       // Výchozí field pro vlhkost
int tsFieldPress = 5;     // Výchozí field pro tlak
int tsFieldCO2 = 6;       // Výchozí field pro CO2
int tsFieldBatt = 4;      // Výchozí field pro napětí baterie

// Funkce pro výpočet checksum
uint32_t calculateChecksum(const Config* cfg) {
  uint32_t checksum = 0;
  const uint8_t* data = (const uint8_t*)cfg;
  // Počítáme checksum ze všech dat kromě samotného checksum pole
  for (size_t i = 0; i < sizeof(Config) - sizeof(uint32_t); i++) {
    checksum += data[i];
  }
  return checksum;
}

// Funkce pro měření napětí baterie s průměrováním
float getBatteryVoltage() {
  float sum = 0;
  const int samples = 5;
  for (int i = 0; i < samples; i++) {
    sum += analogReadMilliVolts(ADC_IN) * DIVIDER_RATIO / 1000;
    delay(10);
  }
  float voltage = sum / samples;
  Serial.print("Napětí baterie: ");
  Serial.print(voltage);
  Serial.println("V");
  return voltage;
}

// Funkce pro detekci napájení z baterie
bool isRunningOnBattery() {
  float voltage = getBatteryVoltage();
  // Pokud je napětí nižší než prahová hodnota, pravděpodobně běžíme na baterii
  return (voltage < BATTERY_THRESHOLD);
}

// Funkce pro inicializaci konfigurace
void initConfig() {
  // Inicializace konfigurace z globálních proměnných
  strcpy(config.serverAddressTmep, serverAddressTmep);
  strcpy(config.thingSpeakApiKey, thingSpeakApiKey);
  strcpy(config.thingSpeakChannelId, thingSpeakChannelId);
  config.sleepTime = sleepTime;
  config.sensorType = sensorType;
  config.serverSendType = serverSendType;
  config.sdaPin = sdaPin;
  config.sclPin = sclPin;
  config.tsFieldTemp = tsFieldTemp;
  config.tsFieldHum = tsFieldHum;
  config.tsFieldPress = tsFieldPress;
  config.tsFieldCO2 = tsFieldCO2;
  config.tsFieldBatt = tsFieldBatt;
  config.configVersion = CONFIG_VERSION;
  config.checksum = calculateChecksum(&config);
}

// Vytvoření instancí
WiFiManager wm;
SensirionI2cSht4x sht4x;
Adafruit_BME280 bme;
SensirionI2cScd4x SCD4X;
OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature DS18B20(&oneWire);
DeviceAddress sensorAddress;

// Proměnná pro sledování stavu konfiguračního portálu
bool portalRunning = false;

// Rozšířené parametry WiFi manageru - budou inicializovány dynamicky
WiFiManagerParameter* custom_serverAddressTmep = nullptr;
WiFiManagerParameter* custom_thingSpeakApiKey = nullptr;
WiFiManagerParameter* custom_thingSpeakChannelId = nullptr;
WiFiManagerParameter* custom_sleepTime = nullptr;
WiFiManagerParameter* custom_sensorType = nullptr;
WiFiManagerParameter* custom_serverSendType = nullptr;
WiFiManagerParameter* custom_sdaPin = nullptr;
WiFiManagerParameter* custom_sclPin = nullptr;
WiFiManagerParameter* custom_tsFieldTemp = nullptr;
WiFiManagerParameter* custom_tsFieldHum = nullptr;
WiFiManagerParameter* custom_tsFieldPress = nullptr;
WiFiManagerParameter* custom_tsFieldCO2 = nullptr;
WiFiManagerParameter* custom_tsFieldBatt = nullptr;

// Funkce pro validaci konfigurace
void validateConfig() {
  if (config.sleepTime < 1 || config.sleepTime > 200) {
    Serial.println("Neplatná doba spánku, nastavuji na výchozích 5 minut");
    config.sleepTime = 5;
  }

  if (config.sensorType < 0 || (config.sensorType > 5 && config.sensorType != 99)) {
    Serial.println("Neplatný typ senzoru, nastavuji na auto-detekci");
    config.sensorType = 99;
  }

  if (config.serverSendType < 0 || config.serverSendType > 3) {
    Serial.println("Neplatný typ odesílání, nastavuji na obě platformy");
    config.serverSendType = ServerType::BOTH;
  }

  if (config.sdaPin < 0 || config.sdaPin > 40) {
    Serial.println("Neplatný SDA pin, nastavuji na výchozí hodnotu");
    config.sdaPin = DEFAULT_SDA_PIN;
  }

  if (config.sclPin < 0 || config.sclPin > 40) {
    Serial.println("Neplatný SCL pin, nastavuji na výchozí hodnotu");
    config.sclPin = DEFAULT_SCL_PIN;
  }

  // Validace ThingSpeak fieldů (1-8)
  if (config.tsFieldTemp < 1 || config.tsFieldTemp > 8) config.tsFieldTemp = 2;
  if (config.tsFieldHum < 1 || config.tsFieldHum > 8) config.tsFieldHum = 3;
  if (config.tsFieldPress < 1 || config.tsFieldPress > 8) config.tsFieldPress = 5;
  if (config.tsFieldCO2 < 1 || config.tsFieldCO2 > 8) config.tsFieldCO2 = 6;
  if (config.tsFieldBatt < 1 || config.tsFieldBatt > 8) config.tsFieldBatt = 4;
}

// Funkce pro uložení konfigurace do EEPROM
void saveConfig() {
  // Nastavení verze konfigurace a checksum
  config.configVersion = CONFIG_VERSION;
  config.checksum = calculateChecksum(&config);

  // Uložení celé struktury do EEPROM
  EEPROM.put(0, config);
  EEPROM.commit();

  Serial.println("Konfigurace uložena do EEPROM");
  Serial.println("TMEP server: " + String(config.serverAddressTmep));
  Serial.println("ThingSpeak API: " + String(config.thingSpeakApiKey));
  Serial.println("ThingSpeak Channel: " + String(config.thingSpeakChannelId));
  Serial.println("Sleep time: " + String(config.sleepTime));
}

// Funkce pro načtení konfigurace z EEPROM
bool loadConfig() {
  // Načtení celé struktury z EEPROM
  EEPROM.get(0, config);

  // Kontrola verze konfigurace
  if (config.configVersion != CONFIG_VERSION) {
    Serial.println("Neplatná verze konfigurace (" + String(config.configVersion) + " != " + String(CONFIG_VERSION) + "), nastavuji výchozí hodnoty");
    resetConfig();
    return false;
  }

  // Kontrola integrity dat pomocí checksum
  uint32_t calculatedChecksum = calculateChecksum(&config);
  if (config.checksum != calculatedChecksum) {
    Serial.println("Poškozená konfigurace (checksum nesouhlasí), nastavuji výchozí hodnoty");
    resetConfig();
    return false;
  }

  // Validace načtených hodnot
  validateConfig();
  return true;
}

// Funkce pro aktualizaci globálních proměnných z konfigurace
void updateGlobalVariables() {
  // Aktualizace globálních proměnných z konfigurace
  strcpy(serverAddressTmep, config.serverAddressTmep);
  strcpy(thingSpeakApiKey, config.thingSpeakApiKey);
  strcpy(thingSpeakChannelId, config.thingSpeakChannelId);
  sleepTime = config.sleepTime;
  sensorType = config.sensorType;
  serverSendType = config.serverSendType;
  sdaPin = config.sdaPin;
  sclPin = config.sclPin;
  tsFieldTemp = config.tsFieldTemp;
  tsFieldHum = config.tsFieldHum;
  tsFieldPress = config.tsFieldPress;
  tsFieldCO2 = config.tsFieldCO2;
  tsFieldBatt = config.tsFieldBatt;
}

// Funkce pro reset konfigurace na výchozí hodnoty
void resetConfig() {
  // Resetování globálních proměnných na výchozí hodnoty
  strcpy(serverAddressTmep, "xny3ef-8khshy.tmep.cz/index.php");
  strcpy(thingSpeakApiKey, "ABS8T4OULVYA1FOL");
  strcpy(thingSpeakChannelId, "845449");
  sleepTime = 5;
  sensorType = 99;
  serverSendType = ServerType::BOTH;
  sdaPin = DEFAULT_SDA_PIN;
  sclPin = DEFAULT_SCL_PIN;
  tsFieldTemp = 2;
  tsFieldHum = 3;
  tsFieldPress = 5;
  tsFieldCO2 = 6;
  tsFieldBatt = 4;

  // Inicializace konfigurace z globálních proměnných
  initConfig();

  // Uložení konfigurace do EEPROM
  saveConfig();

  Serial.println("Konfigurace resetována na výchozí hodnoty");
}

void saveConfigCallback() {
  Serial.println("Ukládání konfigurace...");

  // Uložení parametrů do struktury konfigurace
  if (custom_serverAddressTmep) strcpy(config.serverAddressTmep, custom_serverAddressTmep->getValue());
  if (custom_thingSpeakApiKey) strcpy(config.thingSpeakApiKey, custom_thingSpeakApiKey->getValue());
  if (custom_thingSpeakChannelId) strcpy(config.thingSpeakChannelId, custom_thingSpeakChannelId->getValue());
  if (custom_sleepTime) config.sleepTime = atoi(custom_sleepTime->getValue());
  if (custom_sensorType) config.sensorType = atoi(custom_sensorType->getValue());
  if (custom_serverSendType) config.serverSendType = atoi(custom_serverSendType->getValue());
  if (custom_sdaPin) config.sdaPin = atoi(custom_sdaPin->getValue());
  if (custom_sclPin) config.sclPin = atoi(custom_sclPin->getValue());

  // Uložení ThingSpeak fieldů
  if (custom_tsFieldTemp) config.tsFieldTemp = atoi(custom_tsFieldTemp->getValue());
  if (custom_tsFieldHum) config.tsFieldHum = atoi(custom_tsFieldHum->getValue());
  if (custom_tsFieldPress) config.tsFieldPress = atoi(custom_tsFieldPress->getValue());
  if (custom_tsFieldCO2) config.tsFieldCO2 = atoi(custom_tsFieldCO2->getValue());
  if (custom_tsFieldBatt) config.tsFieldBatt = atoi(custom_tsFieldBatt->getValue());

  // Validace a uložení konfigurace
  validateConfig();
  saveConfig();

  // Aktualizace globálních proměnných
  updateGlobalVariables();
}

// Funkce pro vytvoření WiFiManager parametrů s aktuálními hodnotami
void createWiFiManagerParameters() {
  // Uvolnění starých parametrů pokud existují
  if (custom_serverAddressTmep) delete custom_serverAddressTmep;
  if (custom_thingSpeakApiKey) delete custom_thingSpeakApiKey;
  if (custom_thingSpeakChannelId) delete custom_thingSpeakChannelId;
  if (custom_sleepTime) delete custom_sleepTime;
  if (custom_sensorType) delete custom_sensorType;
  if (custom_serverSendType) delete custom_serverSendType;
  if (custom_sdaPin) delete custom_sdaPin;
  if (custom_sclPin) delete custom_sclPin;
  if (custom_tsFieldTemp) delete custom_tsFieldTemp;
  if (custom_tsFieldHum) delete custom_tsFieldHum;
  if (custom_tsFieldPress) delete custom_tsFieldPress;
  if (custom_tsFieldCO2) delete custom_tsFieldCO2;
  if (custom_tsFieldBatt) delete custom_tsFieldBatt;

  // Vytvoření nových parametrů s aktuálními hodnotami
  custom_serverAddressTmep = new WiFiManagerParameter("tmepServer", "TMEP.CZ adresa (max 40 znaků)", serverAddressTmep, 40);
  custom_thingSpeakApiKey = new WiFiManagerParameter("thingSpeakKey", "ThingSpeak API klíč", thingSpeakApiKey, 40);
  custom_thingSpeakChannelId = new WiFiManagerParameter("thingSpeakChannel", "ThingSpeak Channel ID", thingSpeakChannelId, 20);
  custom_sleepTime = new WiFiManagerParameter("sleepTime", "Doba spánku (max 200 minut)", String(sleepTime).c_str(), 6);
  custom_sensorType = new WiFiManagerParameter("sensorType", "Typ senzoru (99: auto, 0-5)", String(sensorType).c_str(), 2);
  custom_serverSendType = new WiFiManagerParameter("serverType", "Cíl odesílání (0:Žádný, 1:TMEP, 2:ThingSpeak, 3:Oba)", String(serverSendType).c_str(), 2);
  custom_sdaPin = new WiFiManagerParameter("sdaPin", "I2C SDA pin", String(sdaPin).c_str(), 3);
  custom_sclPin = new WiFiManagerParameter("sclPin", "I2C SCL pin", String(sclPin).c_str(), 3);

  // Parametry pro ThingSpeak fieldy (kanály)
  custom_tsFieldTemp = new WiFiManagerParameter("tsFieldTemp", "ThingSpeak field pro teplotu (1-8)", String(tsFieldTemp).c_str(), 2);
  custom_tsFieldHum = new WiFiManagerParameter("tsFieldHum", "ThingSpeak field pro vlhkost (1-8)", String(tsFieldHum).c_str(), 2);
  custom_tsFieldPress = new WiFiManagerParameter("tsFieldPress", "ThingSpeak field pro tlak (1-8)", String(tsFieldPress).c_str(), 2);
  custom_tsFieldCO2 = new WiFiManagerParameter("tsFieldCO2", "ThingSpeak field pro CO2 (1-8)", String(tsFieldCO2).c_str(), 2);
  custom_tsFieldBatt = new WiFiManagerParameter("tsFieldBatt", "ThingSpeak field pro napětí baterie (1-8)", String(tsFieldBatt).c_str(), 2);
}

// Funkce pro nastavení menu WiFiManageru
void setupWiFiManager() {
  // Vytvoření parametrů s aktuálními hodnotami
  createWiFiManagerParameters();

  // Přidání parametrů do WiFi manageru
  wm.addParameter(custom_serverAddressTmep);
  wm.addParameter(custom_thingSpeakApiKey);
  wm.addParameter(custom_thingSpeakChannelId);
  wm.addParameter(custom_sleepTime);
  wm.addParameter(custom_sensorType);
  wm.addParameter(custom_serverSendType);
  wm.addParameter(custom_sdaPin);
  wm.addParameter(custom_sclPin);

  // Přidání parametrů pro ThingSpeak fieldy
  wm.addParameter(custom_tsFieldTemp);
  wm.addParameter(custom_tsFieldHum);
  wm.addParameter(custom_tsFieldPress);
  wm.addParameter(custom_tsFieldCO2);
  wm.addParameter(custom_tsFieldBatt);

  // Nastavení callback funkce pro uložení konfigurace
  wm.setSaveConfigCallback(saveConfigCallback);

  // Nastavení parametrů na vlastní stránku a oddělení od WiFi stránky
  wm.setParamsPage(true);

  // Nastavení, aby se callback volal i při neúspěšném připojení k WiFi
  wm.setBreakAfterConfig(true);

  // Nastavení vlastního menu s více položkami
  std::vector<const char *> menu = {"wifi","param","info","sep","restart","sep","erase","exit"};
  wm.setMenu(menu);

  // Nastavení tmavého režimu
  wm.setDarkMode(true);

  // Nastavení vlastního titulku
  wm.setTitle("Laskakit Meteo Mini - Konfigurace");

  // Nastavení hostname
  wm.setHostname("LaskakitMeteoMini");

  // Nastavení timeoutu pro konfigurační portál podle typu napájení
  if (isRunningOnBattery()) {
    Serial.println("Napájení z baterie - nastavuji kratší timeout");
    wm.setConfigPortalTimeout(CONFIG_TIMEOUT_BATTERY); // Kratší timeout při napájení z baterie
  } else {
    Serial.println("Externí napájení - nastavuji delší timeout");
    wm.setConfigPortalTimeout(CONFIG_TIMEOUT_EXTERNAL); // Delší timeout při externím napájení
  }

  // Nastavení zobrazení tlačítek na informační stránce
  wm.setShowInfoErase(true);

  // Nastavení zobrazení hesla
  wm.setShowPassword(true);

  // Nastavení HTTP portu
  wm.setHttpPort(80);

  // Nastavení WiFi kanálu pro AP
  wm.setWiFiAPChannel(1);
}

// Funkce pro kontrolu OnDemand konfigurace
void checkButton() {
  // Pokud je portál spuštěn, zpracovat požadavky
  if (portalRunning) {
    wm.process();
  }

  // Kontrola, zda je tlačítko stisknuto
  if (digitalRead(TRIGGER_PIN) == LOW) {
    delay(50);  // Debounce
    if (digitalRead(TRIGGER_PIN) == LOW) {
      if (!portalRunning) {
        Serial.println("Tlačítko stisknuto, spouštím konfigurační portál");

        // Inicializace EEPROM před resetováním nastavení (pro jistotu, i když by měla být již inicializována)
        EEPROM.begin(EEPROM_SIZE);

        // ODSTRANĚNO: fullReset() - toto bylo hlavní příčinou problému!
        // Místo resetování načteme aktuální konfiguraci
        loadConfig();
        updateGlobalVariables();

        // Nastavení WiFiManageru
        setupWiFiManager();

        // Spuštění konfiguračního portálu
        if (!wm.startConfigPortal("Laskakit Meteo Mini Config")) {
          Serial.println("Nepodařilo se spustit konfigurační portál nebo vypršel timeout");
          portalRunning = false;

           // Pokud jsme na baterii, přejdeme do režimu spánku
          if (isRunningOnBattery()) {
            Serial.println("Napájení z baterie - přecházím do režimu spánku");
            esp_sleep_enable_timer_wakeup(sleepTime * 60 * 1000000ULL);
            esp_deep_sleep_start();
          }
        } else {
          Serial.println("Připojeno k WiFi přes konfigurační portál");
          // Nastavíme portalRunning na true, aby wm.process() byl volán v checkButton()
          portalRunning = true;
        }
      } else {
        Serial.println("Tlačítko stisknuto, zastavuji konfigurační portál");
        wm.stopWebPortal();
        portalRunning = false;
      }
    }
  }
}

// Funkce pro odeslání dat na ThingSpeak
bool sendToThingSpeak(float temperature, float humidity, float pressure, uint16_t co2, float batteryVoltage) {
  // Kontrola, zda jsou vyplněny povinné údaje
  if (strlen(thingSpeakApiKey) == 0 || strlen(thingSpeakChannelId) == 0) {
    Serial.println("Chybí ThingSpeak API klíč nebo Channel ID");
    return false;
  }

  HTTPClient http;
  String url = "http://api.thingspeak.com/update";

  // Dynamické vytvoření POST requestu podle konfigurace
  String postData = String("api_key=") + thingSpeakApiKey;

  // Přidání hodnot podle nakonfigurovaných fieldů
  postData += "&field" + String(tsFieldTemp) + "=" + String(temperature);

  if ((sensorType == 0) || (sensorType == 1) || (sensorType == 2) || (sensorType == 3) || (sensorType == 4)) {
    postData += "&field" + String(tsFieldHum) + "=" + String(humidity);
  }

  if ((sensorType == 2) || (sensorType == 3)) {
    postData += "&field" + String(tsFieldPress) + "=" + String(pressure);
  }

  if (sensorType == 4) {
    postData += "&field" + String(tsFieldCO2) + "=" + String(co2);
  }

  postData += "&field" + String(tsFieldBatt) + "=" + String(batteryVoltage);

  http.begin(url);
  http.addHeader("Content-Type", "application/x-www-form-urlencoded");

  int httpResponseCode = http.POST(postData);
  http.end();

  Serial.print("ThingSpeak HTTP kód: ");
  Serial.println(httpResponseCode);

  return (httpResponseCode > 0);
}

void setup() {
  // Inicializace sériové komunikace
  Serial.begin(115200);
  delay(100);

  // Inkrementace počtu bootů
  bootCount++;

  // Výpis důvodu probuzení
  esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();

  Serial.println("\n Starting");
  Serial.printf("Boot count: %d\n", bootCount);

  // Výpis důvodu probuzení
  switch(wakeup_reason) {
    case ESP_SLEEP_WAKEUP_TIMER:
      Serial.println("Probuzení způsobeno časovačem");
      break;
    case ESP_SLEEP_WAKEUP_GPIO:
      Serial.println("Probuzení způsobeno tlačítkem (GPIO)");
      break;
    default:
      Serial.printf("Probuzení nebylo způsobeno deep sleep: %d\n", wakeup_reason);
      break;
  }

  // Kontrola stavu vstupního pinu tlačítka při startu
  pinMode(TRIGGER_PIN, INPUT_PULLUP);

  // Přidáno zpoždění pro stabilizaci po probuzení z deep sleep
  delay(100);

  // Inicializace EEPROM a načtení uložené konfigurace
  EEPROM.begin(EEPROM_SIZE);

  // Inicializace konfigurace z globálních proměnných
  initConfig();

  // Kontrola, zda bylo probuzení způsobeno tlačítkem nebo je tlačítko stisknuto při startu
  if (wakeup_reason == ESP_SLEEP_WAKEUP_GPIO || digitalRead(TRIGGER_PIN) == LOW) {
    Serial.println("Tlačítko je stisknuto nebo probuzení bylo způsobeno tlačítkem. Vstupuji do konfiguračního režimu.");

    // Delší zpoždění pro stabilizaci systému před spuštěním WiFiManager
    delay(500);

    // Načtení aktuální konfigurace místo resetování
    if (loadConfig()) {
      Serial.println("Konfigurace načtena pro konfigurační režim");
      updateGlobalVariables();
    } else {
      Serial.println("Použiji výchozí hodnoty pro konfigurační režim");
    }
