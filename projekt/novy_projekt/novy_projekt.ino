/**
 * @file meteomini2_arduino.ino
 * @brief Professional IoT Weather Station for ESP32-C3 (Arduino IDE Compatible)
 * @version 2.0.0
 * <AUTHOR> Agent (Arduino IDE Compatible Version)
 * 
 * Features:
 * - Multi-sensor support (SHT40, BME280, SCD41, DS18B20)
 * - Dual server support (TMEP.cz, ThingSpeak)
 * - WiFi configuration portal with LED indication
 * - Deep sleep power management
 * - Battery monitoring with emergency modes
 * - Non-blocking operations
 * - Robust error handling
 * - Arduino IDE compatible
 */

#include <WiFi.h>
#include <WiFiManager.h>
#include <HTTPClient.h>
#include <EEPROM.h>
#include <Wire.h>
#include <SensirionI2cSht4x.h>
#include <Adafruit_BME280.h>
#include <SensirionI2cScd4x.h>
#include <OneWire.h>
#include <DallasTemperature.h>

// ============================================================================
// CONSTANTS AND CONFIGURATION
// ============================================================================

// Hardware Configuration - OPRAVENÉ PINY PODĽA DOKUMENTÁCIE
#define LED_PIN 7
#define TRIGGER_PIN 2
#define PWR_PIN 3
#define ONEWIRE_PIN 10  // OPRAVENÉ: DS18B20 pin podľa dokumentácie
#define DEFAULT_SDA_PIN 19  // OPRAVENÉ: I2C SDA pin podľa dokumentácie
#define DEFAULT_SCL_PIN 18  // OPRAVENÉ: I2C SCL pin podľa dokumentácie

// Power Management - VYLEPŠENÉ PRAHY A LIMITY
#define BATTERY_LOW_THRESHOLD 3.2f  // ZVÝŠENÉ pre bezpečnosť Li-Po batérií
#define EXTERNAL_POWER_THRESHOLD 4.2f  // ZNÍŽENÉ pre lepšiu detekciu
#define EMERGENCY_SLEEP_TIME 60 // minutes
#define MAX_WIFI_CONNECT_ATTEMPTS 3  // ZNÍŽENÉ pre úsporu energie
#define BATTERY_CRITICAL_THRESHOLD 2.8f  // NOVÉ: kritická úroveň batérie

// Timing Configuration
#define LED_BLINK_INTERVAL 500
#define PORTAL_AUTO_CLOSE_TIME 300000 // 5 minutes
#define CONFIG_TIMEOUT_BATTERY 120    // 2 minutes
#define CONFIG_TIMEOUT_EXTERNAL 300   // 5 minutes

// EEPROM Configuration
#define EEPROM_SIZE 512
#define CONFIG_VERSION 2

// Sensor Types
#define SENSOR_SHT40_0x44 0
#define SENSOR_SHT40_0x45 1
#define SENSOR_BME280_0x76 2
#define SENSOR_BME280_0x77 3
#define SENSOR_SCD41 4
#define SENSOR_DS18B20 5
#define SENSOR_AUTO_DETECT 99

// Server Types
#define SERVER_NONE 0
#define SERVER_TMEP 1
#define SERVER_THINGSPEAK 2
#define SERVER_BOTH 3

// ============================================================================
// CONFIGURATION STRUCTURE
// ============================================================================

struct Config {
    uint8_t configVersion;
    char serverAddressTmep[40];
    char thingSpeakApiKey[40];
    char thingSpeakChannelId[20];
    uint16_t sleepTime;
    uint8_t sensorType;
    uint8_t serverSendType;
    uint8_t sdaPin;
    uint8_t sclPin;
    uint8_t tsFieldTemp;
    uint8_t tsFieldHum;
    uint8_t tsFieldPress;
    uint8_t tsFieldCO2;
    uint8_t tsFieldBatt;
    
    bool isValid() {
        return configVersion == CONFIG_VERSION &&
               sleepTime > 0 && sleepTime <= 200 &&
               sensorType <= 99 &&
               serverSendType <= 3;
    }
    
    void setDefaults() {
        configVersion = CONFIG_VERSION;
        strcpy(serverAddressTmep, "xny3ef-8khshy.tmep.cz/index.php");
        strcpy(thingSpeakApiKey, "ABS8T4OULVYA1FOL");
        strcpy(thingSpeakChannelId, "845449");
        sleepTime = 5;
        sensorType = SENSOR_AUTO_DETECT;
        serverSendType = SERVER_BOTH;
        sdaPin = DEFAULT_SDA_PIN;
        sclPin = DEFAULT_SCL_PIN;
        tsFieldTemp = 2;
        tsFieldHum = 3;
        tsFieldPress = 5;
        tsFieldCO2 = 6;
        tsFieldBatt = 4;
    }
};

// ============================================================================
// GLOBAL VARIABLES
// ============================================================================

// Configuration
Config config;

// Hardware Objects
WiFiManager wifiManager;
SensirionI2cSht4x sht4x;
Adafruit_BME280 bme;
SensirionI2cScd4x scd4x;
OneWire oneWire(ONEWIRE_PIN);
DallasTemperature ds18b20(&oneWire);

// State Variables
RTC_DATA_ATTR uint32_t bootCount = 0;
RTC_DATA_ATTR uint8_t failedConnectAttempts = 0;
RTC_DATA_ATTR uint32_t totalSuccessfulSends = 0;
RTC_DATA_ATTR uint32_t totalFailedSends = 0;
RTC_DATA_ATTR float lastValidTemperature = NAN;
RTC_DATA_ATTR uint32_t lastSuccessfulSendTime = 0;

// Portal State
volatile bool portalRunning = false;
volatile bool ledState = false;
uint32_t lastLedBlink = 0;
uint32_t portalStartTime = 0;

// Sensor Data
DeviceAddress sensorAddress;
const char* version = "2.0.0";

// WiFiManager Parameters
WiFiManagerParameter* customParams[13];
bool parametersInitialized = false;

// ============================================================================
// SENSOR DATA STRUCTURE
// ============================================================================

struct SensorData {
    float temperature;
    float humidity;
    float pressure;
    uint16_t co2;
    bool isValid;
    
    SensorData() {
        temperature = 0.0f;
        humidity = 0.0f;
        pressure = 0.0f;
        co2 = 0;
        isValid = false;
    }
    
    bool validateData() {
        // Rozšírená validácia teploty s rôznymi limitmi pre rôzne senzory
        float tempMin = -40.0f, tempMax = 85.0f;
        if (config.sensorType == SENSOR_DS18B20) {
            tempMin = -55.0f; tempMax = 125.0f;  // DS18B20 má širší rozsah
        } else if (config.sensorType == SENSOR_SCD41) {
            tempMin = -10.0f; tempMax = 60.0f;   // SCD41 má užší rozsah
        }

        if (isnan(temperature) || temperature < tempMin || temperature > tempMax) {
            Serial.printf("Neplatná teplota: %.2f°C (rozsah: %.1f až %.1f°C)\n",
                         temperature, tempMin, tempMax);
            return false;
        }

        // Validácia vlhkosti (pre senzory ktoré ju podporujú)
        if (config.sensorType <= 4 && config.sensorType != SENSOR_DS18B20) {
            if (isnan(humidity) || humidity < 0.0f || humidity > 100.0f) {
                Serial.printf("Neplatná vlhkosť: %.2f%% (rozsah: 0-100%%)\n", humidity);
                return false;
            }
        }

        // Validácia tlaku (iba BME280)
        if (config.sensorType == SENSOR_BME280_0x76 || config.sensorType == SENSOR_BME280_0x77) {
            if (isnan(pressure) || pressure < 300.0f || pressure > 1100.0f) {
                Serial.printf("Neplatný tlak: %.2f hPa (rozsah: 300-1100 hPa)\n", pressure);
                return false;
            }
        }

        // Rozšírená validácia CO2 (iba SCD41)
        if (config.sensorType == SENSOR_SCD41) {
            if (co2 < 400 || co2 > 40000) {  // ROZŠÍRENÝ rozsah
                Serial.printf("Neplatné CO2: %d ppm (rozsah: 400-40000 ppm)\n", co2);
                return false;
            }
            // Varovanie pri vysokých hodnotách
            if (co2 > 5000) {
                Serial.printf("VAROVANIE: Vysoká koncentrácia CO2: %d ppm\n", co2);
            }
        }

        // Kontrola rozumnosti kombinácií hodnôt
        if (!isnan(temperature) && !isnan(humidity)) {
            // Kontrola rosného bodu - vlhkosť nemôže byť 100% pri vysokých teplotách
            if (temperature > 30.0f && humidity > 95.0f) {
                Serial.printf("VAROVANIE: Podozrivá kombinácia T=%.1f°C, RH=%.1f%%\n",
                             temperature, humidity);
            }
        }

        return true;
    }
};

// ============================================================================
// SYSTEM MONITORING AND DIAGNOSTICS FUNCTIONS
// ============================================================================

void printSystemDiagnostics() {
    Serial.println("🔍 Systémová diagnostika:");
    Serial.printf("  📊 Celkovo úspešných odoslaní: %d\n", totalSuccessfulSends);
    Serial.printf("  📊 Celkovo neúspešných odoslaní: %d\n", totalFailedSends);
    Serial.printf("  📊 Úspešnosť: %.1f%%\n",
                 (totalSuccessfulSends + totalFailedSends > 0) ?
                 (100.0f * totalSuccessfulSends / (totalSuccessfulSends + totalFailedSends)) : 0.0f);
    Serial.printf("  💾 Voľná heap pamäť: %d bytes\n", ESP.getFreeHeap());
    Serial.printf("  💾 Najmenšia voľná heap: %d bytes\n", ESP.getMinFreeHeap());
    Serial.printf("  🔄 Počet resetov: %d\n", ESP.getResetReason());

    if (!isnan(lastValidTemperature)) {
        Serial.printf("  🌡️  Posledná platná teplota: %.2f°C\n", lastValidTemperature);
    }
}

void checkMemoryHealth() {
    uint32_t freeHeap = ESP.getFreeHeap();
    uint32_t minFreeHeap = ESP.getMinFreeHeap();

    if (freeHeap < 10000) {  // Menej ako 10KB voľnej pamäte
        Serial.printf("⚠️  VAROVANIE: Nízka voľná pamäť: %d bytes\n", freeHeap);
    }

    if (minFreeHeap < 5000) {  // Kriticky nízka pamäť
        Serial.printf("🚨 KRITICKÉ: Minimálna voľná pamäť: %d bytes\n", minFreeHeap);
        Serial.println("🔄 Reštartujem systém pre uvoľnenie pamäte...");
        ESP.restart();
    }
}

// ============================================================================
// POWER MANAGEMENT FUNCTIONS
// ============================================================================

float getBatteryVoltage() {
    const uint8_t SAMPLES = 10;  // ZVÝŠENÉ pre presnejšie meranie
    float sum = 0;
    float readings[SAMPLES];

    // Zbieranie vzoriek
    for (uint8_t i = 0; i < SAMPLES; i++) {
        readings[i] = analogReadMilliVolts(A0) * 1.********** / 1000.0f;
        delay(20);  // ZVÝŠENÉ zpoždenie pre stabilitu
    }

    // Odstránenie extrémnych hodnôt (outliers)
    for (uint8_t i = 0; i < SAMPLES - 2; i++) {
        for (uint8_t j = i + 1; j < SAMPLES - 1; j++) {
            if (readings[i] > readings[j]) {
                float temp = readings[i];
                readings[i] = readings[j];
                readings[j] = temp;
            }
        }
    }

    // Priemer zo stredných hodnôt (bez najvyššej a najnižšej)
    for (uint8_t i = 1; i < SAMPLES - 1; i++) {
        sum += readings[i];
    }

    float voltage = sum / (SAMPLES - 2);

    // Validácia výsledku
    if (voltage < 0.0f || voltage > 6.0f) {
        Serial.printf("VAROVANIE: Neplatné napätie batérie: %.2fV\n", voltage);
        return 0.0f;
    }

    return voltage;
}

bool isRunningOnBattery() {
    return getBatteryVoltage() < EXTERNAL_POWER_THRESHOLD;
}

bool isBatteryLow() {
    float voltage = getBatteryVoltage();
    return voltage > 0.0f && voltage < BATTERY_LOW_THRESHOLD;
}

bool isBatteryCritical() {
    float voltage = getBatteryVoltage();
    return voltage > 0.0f && voltage < BATTERY_CRITICAL_THRESHOLD;
}

void enterEmergencyMode() {
    float voltage = getBatteryVoltage();
    Serial.printf("EMERGENCY: Kriticky nízka batéria (%.2fV < %.2fV)!\n",
                 voltage, BATTERY_CRITICAL_THRESHOLD);
    Serial.printf("Vstupujem do núdzového spánku na %d minút.\n", EMERGENCY_SLEEP_TIME);

    // Vypnutie všetkých periférií
    digitalWrite(PWR_PIN, LOW);
    WiFi.disconnect(true);
    WiFi.mode(WIFI_OFF);

    // Dlhý núdzový spánok
    esp_sleep_enable_timer_wakeup(EMERGENCY_SLEEP_TIME * 60ULL * 1000000ULL);
    esp_deep_sleep_start();
}

void enterSleep(uint16_t minutes) {
    Serial.printf("Vstupujem do spánku na %d minút\n", minutes);

    // Bezpečnostná kontrola batérie pred spánkom
    if (isBatteryCritical()) {
        Serial.println("VAROVANIE: Kritická úroveň batérie pred spánkom!");
        enterEmergencyMode();
        return;
    }

    // Vypnutie senzorov a periférií
    digitalWrite(PWR_PIN, LOW);

    // Odpojenie od WiFi pre úsporu energie
    WiFi.disconnect(true);
    WiFi.mode(WIFI_OFF);

    // Konfigurácia časovača pre prebúdzanie
    esp_sleep_enable_timer_wakeup(minutes * 60ULL * 1000000ULL);

    // Konfigurácia GPIO prebúdzania (tlačidlo)
    esp_deep_sleep_enable_gpio_wakeup(1ULL << TRIGGER_PIN, ESP_GPIO_WAKEUP_GPIO_LOW);

    Serial.println("Spánok nakonfigurovaný s časovačom a GPIO prebúdzaním");
    Serial.printf("GPIO prebúdzanie povolené na pine %d (LOW úroveň)\n", TRIGGER_PIN);
    Serial.flush();  // Zabezpečenie odoslania všetkých správ
    delay(100);

    esp_deep_sleep_start();
}

// ============================================================================
// LED MANAGEMENT FUNCTIONS
// ============================================================================

void initLED() {
    pinMode(LED_PIN, OUTPUT);
    digitalWrite(LED_PIN, LOW);
}

void startLEDBlinking() {
    digitalWrite(LED_PIN, HIGH);
    ledState = true;
    lastLedBlink = millis();
    portalStartTime = millis();
    Serial.println("LED blinking started");
}

void stopLEDBlinking() {
    digitalWrite(LED_PIN, LOW);
    ledState = false;
    Serial.println("LED blinking stopped");
}

void updateLEDBlink() {
    if (portalRunning && (millis() - lastLedBlink >= LED_BLINK_INTERVAL)) {
        ledState = !ledState;
        digitalWrite(LED_PIN, ledState ? HIGH : LOW);
        lastLedBlink = millis();
    }
}

// ============================================================================
// CONFIGURATION MANAGEMENT FUNCTIONS
// ============================================================================

bool saveConfig() {
    EEPROM.put(0, config);
    bool success = EEPROM.commit();
    if (success) {
        Serial.println("Configuration saved to EEPROM");
    } else {
        Serial.println("Failed to save configuration");
    }
    return success;
}

bool loadConfig() {
    Config tempConfig;
    EEPROM.get(0, tempConfig);
    
    if (tempConfig.isValid()) {
        config = tempConfig;
        Serial.println("Configuration loaded from EEPROM");
        return true;
    } else {
        Serial.println("Invalid configuration, using defaults");
        config.setDefaults();
        saveConfig();
        return false;
    }
}

void resetConfig() {
    config.setDefaults();
    saveConfig();
    Serial.println("Configuration reset to defaults");
}

void printConfig() {
    Serial.println("⚙️  === AKTUÁLNA KONFIGURÁCIA ===");
    Serial.printf("📡 TMEP Server: %s\n", strlen(config.serverAddressTmep) > 0 ? config.serverAddressTmep : "NENAKONFIGUROVANÉ");
    Serial.printf("📊 ThingSpeak Channel: %s\n", strlen(config.thingSpeakChannelId) > 0 ? config.thingSpeakChannelId : "NENAKONFIGUROVANÉ");
    Serial.printf("😴 Doba spánku: %d minút\n", config.sleepTime);

    // Popis typu senzora
    const char* sensorNames[] = {
        "SHT40 (0x44)", "SHT40 (0x45)", "BME280 (0x76)", "BME280 (0x77)",
        "SCD41", "DS18B20", "NEZNÁMY"
    };
    int sensorIndex = (config.sensorType <= 5) ? config.sensorType : 6;
    if (config.sensorType == 99) {
        Serial.println("🔍 Typ senzora: AUTO-DETEKCIA");
    } else {
        Serial.printf("🌡️  Typ senzora: %s (%d)\n", sensorNames[sensorIndex], config.sensorType);
    }

    // Popis typu servera
    Serial.print("📤 Odosielanie dát: ");
    switch (config.serverSendType) {
        case SERVER_NONE: Serial.println("VYPNUTÉ"); break;
        case SERVER_TMEP: Serial.println("TMEP.cz"); break;
        case SERVER_THINGSPEAK: Serial.println("ThingSpeak"); break;
        case SERVER_BOTH: Serial.println("TMEP.cz + ThingSpeak"); break;
        default: Serial.printf("NEZNÁME (%d)\n", config.serverSendType); break;
    }

    Serial.printf("🔌 I2C piny: SDA=%d, SCL=%d\n", config.sdaPin, config.sclPin);
    Serial.printf("📋 ThingSpeak polia: T=%d, H=%d, P=%d, CO2=%d, Bat=%d\n",
                 config.tsFieldTemp, config.tsFieldHum, config.tsFieldPress,
                 config.tsFieldCO2, config.tsFieldBatt);
    Serial.println(String('=', 40));
}

// ============================================================================
// SENSOR MANAGEMENT FUNCTIONS
// ============================================================================

bool initSHT40(uint8_t address) {
    sht4x.begin(Wire, address);
    uint32_t serialNumber;
    uint16_t error = sht4x.serialNumber(serialNumber);

    if (error == 0) {
        Serial.printf("SHT40 sensor found at 0x%02X\n", address);
        return true;
    }
    return false;
}

bool initBME280(uint8_t address) {
    if (bme.begin(address)) {
        Serial.printf("BME280 sensor found at 0x%02X\n", address);
        bme.setSampling(Adafruit_BME280::MODE_FORCED,
                       Adafruit_BME280::SAMPLING_X1,
                       Adafruit_BME280::SAMPLING_X1,
                       Adafruit_BME280::SAMPLING_X1,
                       Adafruit_BME280::FILTER_OFF);
        return true;
    }
    return false;
}

bool initSCD41() {
    scd4x.begin(Wire, 0x62);
    uint16_t error = scd4x.startPeriodicMeasurement();

    if (error == 0) {
        Serial.println("SCD41 sensor found");
        delay(200); // Warm-up time
        return true;
    }
    return false;
}

bool initDS18B20() {
    if (oneWire.search(sensorAddress)) {
        Serial.println("DS18B20 sensor found");
        return true;
    }
    oneWire.reset_search();
    return false;
}

bool initializeSpecificSensor(uint8_t sensorType) {
    switch (sensorType) {
        case SENSOR_SHT40_0x44:
            return initSHT40(0x44);
        case SENSOR_SHT40_0x45:
            return initSHT40(0x45);
        case SENSOR_BME280_0x76:
            return initBME280(0x76);
        case SENSOR_BME280_0x77:
            return initBME280(0x77);
        case SENSOR_SCD41:
            return initSCD41();
        case SENSOR_DS18B20:
            return initDS18B20();
        default:
            return false;
    }
}

bool autoDetectSensor() {
    Serial.println("Auto-detecting sensors...");

    // Try SHT40 0x44
    if (initializeSpecificSensor(SENSOR_SHT40_0x44)) {
        config.sensorType = SENSOR_SHT40_0x44;
        return true;
    }

    // Try SHT40 0x45
    if (initializeSpecificSensor(SENSOR_SHT40_0x45)) {
        config.sensorType = SENSOR_SHT40_0x45;
        return true;
    }

    // Try BME280 0x76
    if (initializeSpecificSensor(SENSOR_BME280_0x76)) {
        config.sensorType = SENSOR_BME280_0x76;
        return true;
    }

    // Try BME280 0x77
    if (initializeSpecificSensor(SENSOR_BME280_0x77)) {
        config.sensorType = SENSOR_BME280_0x77;
        return true;
    }

    // Try SCD41
    if (initializeSpecificSensor(SENSOR_SCD41)) {
        config.sensorType = SENSOR_SCD41;
        return true;
    }

    // Try DS18B20
    if (initializeSpecificSensor(SENSOR_DS18B20)) {
        config.sensorType = SENSOR_DS18B20;
        return true;
    }

    Serial.println("WARNING: No sensor detected!");
    return false;
}

bool initializeSensors() {
    Serial.println("Initializing sensors...");

    // Power up sensors
    pinMode(PWR_PIN, OUTPUT);
    digitalWrite(PWR_PIN, HIGH);
    delay(100);

    // Initialize I2C
    Wire.begin(config.sdaPin, config.sclPin);

    // Initialize OneWire
    ds18b20.begin();

    // Try to detect sensors
    if (config.sensorType == SENSOR_AUTO_DETECT) {
        return autoDetectSensor();
    } else {
        return initializeSpecificSensor(config.sensorType);
    }
}

bool readSHT40(SensorData& data) {
    uint16_t error = sht4x.measureHighPrecision(data.temperature, data.humidity);
    if (error != 0) {
        Serial.println("Error reading SHT40 sensor");
        return false;
    }
    return true;
}

bool readBME280(SensorData& data) {
    bme.takeForcedMeasurement();
    data.temperature = bme.readTemperature();
    data.humidity = bme.readHumidity();
    data.pressure = bme.readPressure() / 100.0f;
    return true;
}

bool readSCD41(SensorData& data) {
    uint16_t error = scd4x.readMeasurement(data.co2, data.temperature, data.humidity);
    if (error != 0) {
        Serial.println("Error reading SCD41 sensor");
        return false;
    }
    return true;
}

bool readDS18B20(SensorData& data) {
    ds18b20.requestTemperatures();
    data.temperature = ds18b20.getTempC(sensorAddress);

    if (data.temperature == DEVICE_DISCONNECTED_C) {
        Serial.println("Error reading DS18B20 sensor");
        return false;
    }
    return true;
}

SensorData readSensorData() {
    SensorData data;

    switch (config.sensorType) {
        case SENSOR_SHT40_0x44:
        case SENSOR_SHT40_0x45:
            data.isValid = readSHT40(data);
            break;
        case SENSOR_BME280_0x76:
        case SENSOR_BME280_0x77:
            data.isValid = readBME280(data);
            break;
        case SENSOR_SCD41:
            data.isValid = readSCD41(data);
            break;
        case SENSOR_DS18B20:
            data.isValid = readDS18B20(data);
            break;
        default:
            data.isValid = false;
    }

    if (data.isValid) {
        data.isValid = data.validateData();
    }

    return data;
}

// ============================================================================
// NETWORK MANAGEMENT FUNCTIONS
// ============================================================================

void handleConnectionFailure() {
    failedConnectAttempts++;

    Serial.printf("Zlyhanie WiFi pripojenia (pokus %d/%d)\n",
                 failedConnectAttempts, MAX_WIFI_CONNECT_ATTEMPTS);

    // Diagnostika WiFi problémov
    Serial.printf("WiFi status: %d, RSSI: %d dBm\n", WiFi.status(), WiFi.RSSI());

    if (failedConnectAttempts >= MAX_WIFI_CONNECT_ATTEMPTS) {
        Serial.printf("Dosiahnutý maximálny počet pokusov (%d). Vstupujem do núdzového spánku.\n",
                     MAX_WIFI_CONNECT_ATTEMPTS);

        // Reset počítadla po dlhom spánku
        if (failedConnectAttempts > MAX_WIFI_CONNECT_ATTEMPTS + 2) {
            failedConnectAttempts = 0;
            Serial.println("Reset počítadla neúspešných pripojení");
        }

        enterSleep(EMERGENCY_SLEEP_TIME);
        return;
    }

    // Progresívne predlžovanie intervalu (exponenciálny backoff)
    uint16_t baseInterval = isRunningOnBattery() ? 5 : 2;  // Kratší interval pri externom napájaní
    uint16_t sleepMinutes = min(static_cast<uint16_t>(baseInterval * pow(2, failedConnectAttempts - 1)),
                               static_cast<uint16_t>(30));  // Maximum 30 minút
    sleepMinutes = max(sleepMinutes, static_cast<uint16_t>(1));

    Serial.printf("Spím na %d minút pred ďalším pokusom o pripojenie.\n", sleepMinutes);
    enterSleep(sleepMinutes);
}

bool connectToWiFi() {
    if (WiFi.status() == WL_CONNECTED) {
        Serial.printf("WiFi už pripojené: %s (RSSI: %d dBm)\n",
                     WiFi.SSID().c_str(), WiFi.RSSI());
        return true;
    }

    Serial.println("Pokúšam sa pripojiť k WiFi...");

    // Nastavenie WiFi módu a konfigurácie
    WiFi.mode(WIFI_STA);
    WiFi.setAutoReconnect(true);
    WiFi.persistent(true);

    // Adaptívny timeout na základe napájania
    uint8_t connectTimeout = isRunningOnBattery() ? 20 : 30;
    wifiManager.setConnectTimeout(connectTimeout);

    // Nastavenie dodatočných parametrov pre lepšiu konektivitu
    wifiManager.setConnectRetries(3);
    wifiManager.setConfigPortalBlocking(false);

    Serial.printf("Timeout pripojenia: %d sekúnd\n", connectTimeout);

    if (!wifiManager.autoConnect("Laskakit Meteo Mini Config")) {
        Serial.println("Zlyhalo pripojenie k WiFi");

        // Diagnostické informácie
        Serial.printf("Posledný WiFi status: %d\n", WiFi.status());
        Serial.printf("Počet uložených sietí: %d\n", WiFi.scanNetworks());

        handleConnectionFailure();
        return false;
    }

    // Úspešné pripojenie - zobrazenie informácií
    Serial.println("✓ Úspešne pripojené k WiFi");
    Serial.printf("  SSID: %s\n", WiFi.SSID().c_str());
    Serial.printf("  IP adresa: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("  RSSI: %d dBm\n", WiFi.RSSI());
    Serial.printf("  Kanál: %d\n", WiFi.channel());
    Serial.printf("  MAC: %s\n", WiFi.macAddress().c_str());

    // Reset počítadla neúspešných pokusov
    failedConnectAttempts = 0;
    return true;
}

bool sendToTMEP(const SensorData& data, float batteryVoltage) {
    if (config.serverSendType == SERVER_THINGSPEAK ||
        config.serverSendType == SERVER_NONE) {
        return true; // Skip TMEP
    }

    if (strlen(config.serverAddressTmep) == 0) {
        Serial.println("TMEP: Prázdna adresa servera");
        return false;
    }

    HTTPClient http;
    char url[384];  // ZVÝŠENÁ veľkosť bufferu

    // Zostavenie základnej URL s teplotou a RSSI
    int urlLen = snprintf(url, sizeof(url), "http://%s/?temp=%.2f&rssi=%d",
                         config.serverAddressTmep, data.temperature, WiFi.RSSI());

    // Kontrola pretečenia bufferu
    if (urlLen >= sizeof(url) - 50) {
        Serial.println("TMEP: URL príliš dlhá");
        return false;
    }

    // Pridanie dodatočných parametrov podľa typu senzora
    if (config.sensorType <= 4 && config.sensorType != SENSOR_DS18B20) {
        urlLen += snprintf(url + urlLen, sizeof(url) - urlLen, "&humV=%.2f", data.humidity);
    }

    if (config.sensorType == SENSOR_BME280_0x76 || config.sensorType == SENSOR_BME280_0x77) {
        urlLen += snprintf(url + urlLen, sizeof(url) - urlLen, "&pressV=%.2f", data.pressure);
    }

    if (config.sensorType == SENSOR_SCD41) {
        urlLen += snprintf(url + urlLen, sizeof(url) - urlLen, "&CO2=%d", data.co2);
    }

    // Pridanie napätia batérie
    urlLen += snprintf(url + urlLen, sizeof(url) - urlLen, "&voltage=%.2f", batteryVoltage);

    // Nastavenie HTTP timeoutov
    http.setTimeout(10000);  // 10 sekúnd timeout
    http.setConnectTimeout(5000);  // 5 sekúnd connect timeout

    if (!http.begin(url)) {
        Serial.println("TMEP: Chyba pri inicializácii HTTP klienta");
        return false;
    }

    Serial.printf("TMEP URL: %s\n", url);

    int httpCode = http.GET();
    String response = "";

    if (httpCode > 0) {
        response = http.getString();
        Serial.printf("TMEP HTTP odpoveď: %d\n", httpCode);
        if (httpCode == 200) {
            Serial.printf("TMEP odpoveď: %s\n", response.c_str());
        }
    } else {
        Serial.printf("TMEP HTTP chyba: %s\n", http.errorToString(httpCode).c_str());
    }

    http.end();
    return (httpCode == 200);
}

bool sendToThingSpeak(const SensorData& data, float batteryVoltage) {
    if (config.serverSendType == SERVER_TMEP ||
        config.serverSendType == SERVER_NONE) {
        return true; // Skip ThingSpeak
    }

    if (strlen(config.thingSpeakApiKey) == 0 || strlen(config.thingSpeakChannelId) == 0) {
        Serial.println("ThingSpeak: Chýba API kľúč alebo Channel ID");
        return false;
    }

    HTTPClient http;
    const char* url = "http://api.thingspeak.com/update";
    char postData[768];  // ZVÝŠENÁ veľkosť bufferu

    // Zostavenie základných POST dát s API kľúčom a teplotou
    int dataLen = snprintf(postData, sizeof(postData), "api_key=%s&field%d=%.2f",
                          config.thingSpeakApiKey, config.tsFieldTemp, data.temperature);

    // Kontrola pretečenia bufferu
    if (dataLen >= sizeof(postData) - 100) {
        Serial.println("ThingSpeak: POST dáta príliš dlhé");
        return false;
    }

    // Pridanie vlhkosti pre podporované senzory
    if (config.sensorType <= 4 && config.sensorType != SENSOR_DS18B20) {
        dataLen += snprintf(postData + dataLen, sizeof(postData) - dataLen,
                           "&field%d=%.2f", config.tsFieldHum, data.humidity);
    }

    // Pridanie tlaku pre BME280
    if (config.sensorType == SENSOR_BME280_0x76 || config.sensorType == SENSOR_BME280_0x77) {
        dataLen += snprintf(postData + dataLen, sizeof(postData) - dataLen,
                           "&field%d=%.2f", config.tsFieldPress, data.pressure);
    }

    // Pridanie CO2 pre SCD41
    if (config.sensorType == SENSOR_SCD41) {
        dataLen += snprintf(postData + dataLen, sizeof(postData) - dataLen,
                           "&field%d=%d", config.tsFieldCO2, data.co2);
    }

    // Pridanie napätia batérie
    dataLen += snprintf(postData + dataLen, sizeof(postData) - dataLen,
                       "&field%d=%.2f", config.tsFieldBatt, batteryVoltage);

    // Nastavenie HTTP timeoutov
    http.setTimeout(15000);  // 15 sekúnd timeout (ThingSpeak môže byť pomalší)
    http.setConnectTimeout(5000);  // 5 sekúnd connect timeout

    if (!http.begin(url)) {
        Serial.println("ThingSpeak: Chyba pri inicializácii HTTP klienta");
        return false;
    }

    http.addHeader("Content-Type", "application/x-www-form-urlencoded");
    http.addHeader("User-Agent", "LaskakitMeteoMini/2.0");

    Serial.printf("ThingSpeak POST dáta: %s\n", postData);

    int httpCode = http.POST(postData);
    String response = "";

    if (httpCode > 0) {
        response = http.getString();
        Serial.printf("ThingSpeak HTTP odpoveď: %d\n", httpCode);
        if (httpCode == 200) {
            Serial.printf("ThingSpeak entry ID: %s\n", response.c_str());
            // Kontrola či je odpoveď platné číslo (entry ID)
            if (response.toInt() > 0) {
                Serial.println("✓ ThingSpeak: Dáta úspešne odoslané");
            } else {
                Serial.printf("ThingSpeak: Neočakávaná odpoveď: %s\n", response.c_str());
            }
        }
    } else {
        Serial.printf("ThingSpeak HTTP chyba: %s\n", http.errorToString(httpCode).c_str());
    }

    http.end();
    return (httpCode == 200 && response.toInt() > 0);
}

// ============================================================================
// WIFI CONFIGURATION FUNCTIONS
// ============================================================================

void saveConfigFromParameters() {
    // Update config from parameters
    strcpy(config.serverAddressTmep, customParams[0]->getValue());
    strcpy(config.thingSpeakApiKey, customParams[1]->getValue());
    strcpy(config.thingSpeakChannelId, customParams[2]->getValue());
    config.sleepTime = atoi(customParams[3]->getValue());
    config.sensorType = atoi(customParams[4]->getValue());
    config.serverSendType = atoi(customParams[5]->getValue());
    config.sdaPin = atoi(customParams[6]->getValue());
    config.sclPin = atoi(customParams[7]->getValue());
    config.tsFieldTemp = atoi(customParams[8]->getValue());
    config.tsFieldHum = atoi(customParams[9]->getValue());
    config.tsFieldPress = atoi(customParams[10]->getValue());
    config.tsFieldCO2 = atoi(customParams[11]->getValue());
    config.tsFieldBatt = atoi(customParams[12]->getValue());

    // Validate and save
    if (!config.isValid()) {
        Serial.println("Invalid configuration received, using defaults");
        config.setDefaults();
    }

    saveConfig();
}

// Static buffers for parameter values to avoid String objects
static char sleepTimeStr[8];
static char sensorTypeStr[4];
static char serverTypeStr[4];
static char sdaPinStr[4];
static char sclPinStr[4];
static char tsFieldTempStr[4];
static char tsFieldHumStr[4];
static char tsFieldPressStr[4];
static char tsFieldCO2Str[4];
static char tsFieldBattStr[4];

void initializeParameters() {
    if (parametersInitialized) return;

    // Convert numeric values to strings using snprintf
    snprintf(sleepTimeStr, sizeof(sleepTimeStr), "%d", config.sleepTime);
    snprintf(sensorTypeStr, sizeof(sensorTypeStr), "%d", config.sensorType);
    snprintf(serverTypeStr, sizeof(serverTypeStr), "%d", config.serverSendType);
    snprintf(sdaPinStr, sizeof(sdaPinStr), "%d", config.sdaPin);
    snprintf(sclPinStr, sizeof(sclPinStr), "%d", config.sclPin);
    snprintf(tsFieldTempStr, sizeof(tsFieldTempStr), "%d", config.tsFieldTemp);
    snprintf(tsFieldHumStr, sizeof(tsFieldHumStr), "%d", config.tsFieldHum);
    snprintf(tsFieldPressStr, sizeof(tsFieldPressStr), "%d", config.tsFieldPress);
    snprintf(tsFieldCO2Str, sizeof(tsFieldCO2Str), "%d", config.tsFieldCO2);
    snprintf(tsFieldBattStr, sizeof(tsFieldBattStr), "%d", config.tsFieldBatt);

    customParams[0] = new WiFiManagerParameter("tmepServer", "TMEP.CZ address", config.serverAddressTmep, 40);
    customParams[1] = new WiFiManagerParameter("thingSpeakKey", "ThingSpeak API Key", config.thingSpeakApiKey, 40);
    customParams[2] = new WiFiManagerParameter("thingSpeakChannel", "ThingSpeak Channel ID", config.thingSpeakChannelId, 20);
    customParams[3] = new WiFiManagerParameter("sleepTime", "Sleep time (minutes)", sleepTimeStr, 6);
    customParams[4] = new WiFiManagerParameter("sensorType", "Sensor type (99=auto)", sensorTypeStr, 2);
    customParams[5] = new WiFiManagerParameter("serverType", "Server type (0=None,1=TMEP,2=ThingSpeak,3=Both)", serverTypeStr, 2);
    customParams[6] = new WiFiManagerParameter("sdaPin", "I2C SDA pin", sdaPinStr, 3);
    customParams[7] = new WiFiManagerParameter("sclPin", "I2C SCL pin", sclPinStr, 3);
    customParams[8] = new WiFiManagerParameter("tsFieldTemp", "ThingSpeak field - Temperature", tsFieldTempStr, 2);
    customParams[9] = new WiFiManagerParameter("tsFieldHum", "ThingSpeak field - Humidity", tsFieldHumStr, 2);
    customParams[10] = new WiFiManagerParameter("tsFieldPress", "ThingSpeak field - Pressure", tsFieldPressStr, 2);
    customParams[11] = new WiFiManagerParameter("tsFieldCO2", "ThingSpeak field - CO2", tsFieldCO2Str, 2);
    customParams[12] = new WiFiManagerParameter("tsFieldBatt", "ThingSpeak field - Battery", tsFieldBattStr, 2);

    parametersInitialized = true;
}

void setupWiFiManager() {
    initializeParameters();

    // Add all parameters
    for (int i = 0; i < 13; i++) {
        wifiManager.addParameter(customParams[i]);
    }

    // Configure WiFiManager
    wifiManager.setSaveConfigCallback([]() {
        Serial.println("Configuration saved via WiFiManager");
        saveConfigFromParameters();
    });

    wifiManager.setAPCallback([](WiFiManager* wm) {
        Serial.println("Entered config mode");
        startLEDBlinking();
        portalRunning = true;
    });

    wifiManager.setWebServerCallback([]() {
        updateLEDBlink();
    });

    wifiManager.setParamsPage(true);
    wifiManager.setBreakAfterConfig(true);
    wifiManager.setDarkMode(true);
    wifiManager.setTitle("Laskakit Meteo Mini - Configuration");
    wifiManager.setHostname("LaskakitMeteoMini");
    wifiManager.setShowPassword(true);
    wifiManager.setHttpPort(80);
    wifiManager.setWiFiAPChannel(1);

    // Set timeout based on power source
    uint16_t timeout = isRunningOnBattery() ?
                      CONFIG_TIMEOUT_BATTERY : CONFIG_TIMEOUT_EXTERNAL;
    wifiManager.setConfigPortalTimeout(timeout);

    // Custom menu - using array instead of vector for better memory management
    const char* menu[] = {"wifi", "param", "info", "sep", "restart", "sep", "erase", "exit"};
    wifiManager.setMenu(menu, 8);
}

bool startConfigPortal() {
    setupWiFiManager();
    startLEDBlinking();
    portalRunning = true;

    bool result = wifiManager.startConfigPortal("Laskakit Meteo Mini Config");

    portalRunning = false;
    stopLEDBlinking();

    if (result) {
        Serial.println("WiFi configuration completed successfully");
    } else {
        Serial.println("WiFi configuration failed or timed out");
    }

    return result;
}

// ============================================================================
// BUTTON HANDLER FUNCTIONS
// ============================================================================

void checkButton() {
    // Handle portal if running
    if (portalRunning) {
        wifiManager.process();
        updateLEDBlink();

        // Auto-close portal after timeout
        if (millis() - portalStartTime > PORTAL_AUTO_CLOSE_TIME) {
            Serial.println("Portal auto-close timeout reached");
            wifiManager.stopWebPortal();
            portalRunning = false;
            stopLEDBlinking();
        }
    }

    // Check button press
    if (digitalRead(TRIGGER_PIN) == LOW) {
        delay(50); // Debounce
        if (digitalRead(TRIGGER_PIN) == LOW) {
            if (!portalRunning) {
                Serial.println("Button pressed - starting config portal");
                if (!startConfigPortal() && isRunningOnBattery()) {
                    enterSleep(config.sleepTime);
                }
            } else {
                Serial.println("Button pressed - stopping config portal");
                wifiManager.stopWebPortal();
                portalRunning = false;
                stopLEDBlinking();
            }
        }
    }
}

// ============================================================================
// MAIN FUNCTIONS
// ============================================================================

void setup() {
    Serial.begin(115200);
    delay(200);  // ZVÝŠENÉ zpoždenie pre stabilizáciu

    Serial.println("\n" + String('=', 60));
    Serial.println("🌡️  Laskakit Meteo Mini v2.0 (Arduino Compatible)");
    Serial.println(String('=', 60));

    // Inicializácia hardvéru
    initLED();
    pinMode(TRIGGER_PIN, INPUT_PULLUP);

    // Diagnostické informácie o systéme
    bootCount++;
    esp_sleep_wakeup_cause_t wakeupReason = esp_sleep_get_wakeup_cause();
    float batteryVoltage = getBatteryVoltage();

    Serial.printf("📊 Boot count: %d\n", bootCount);
    Serial.printf("🔋 Napätie batérie: %.2fV\n", batteryVoltage);
    Serial.printf("⚡ Zdroj napájania: %s\n", isRunningOnBattery() ? "Batéria" : "Externý");
    Serial.printf("💾 Voľná heap pamäť: %d bytes\n", ESP.getFreeHeap());
    Serial.printf("🔧 ESP32 Chip ID: %08X\n", (uint32_t)ESP.getEfuseMac());

    // Diagnostika dôvodu prebúdzania
    switch (wakeupReason) {
        case ESP_SLEEP_WAKEUP_TIMER:
            Serial.println("⏰ Prebúdzanie: Časovač");
            break;
        case ESP_SLEEP_WAKEUP_GPIO:
            Serial.println("🔘 Prebúdzanie: Tlačidlo (GPIO)");
            break;
        case ESP_SLEEP_WAKEUP_EXT0:
            Serial.println("📡 Prebúdzanie: Externý signál 0");
            break;
        case ESP_SLEEP_WAKEUP_EXT1:
            Serial.println("📡 Prebúdzanie: Externý signál 1");
            break;
        case ESP_SLEEP_WAKEUP_TOUCHPAD:
            Serial.println("👆 Prebúdzanie: Dotykový senzor");
            break;
        case ESP_SLEEP_WAKEUP_ULP:
            Serial.println("🔬 Prebúdzanie: ULP koprocesor");
            break;
        default:
            Serial.println("🔌 Prebúdzanie: Zapnutie alebo reset");
            break;
    }

    // Kontrola kritickej úrovne batérie
    if (isBatteryCritical()) {
        Serial.println("🚨 KRITICKÁ ÚROVEŇ BATÉRIE!");
        enterEmergencyMode();
        return;
    }

    // Varovanie pri nízkej batérii
    if (isBatteryLow()) {
        Serial.println("⚠️  VAROVANIE: Nízka úroveň batérie!");
    }

    // Inicializácia EEPROM a načítanie konfigurácie
    Serial.println("📝 Inicializácia EEPROM...");
    EEPROM.begin(EEPROM_SIZE);

    if (loadConfig()) {
        Serial.println("✅ Konfigurácia načítaná z EEPROM");
    } else {
        Serial.println("⚠️  Použitá predvolená konfigurácia");
    }

    printConfig();

    // Zobrazenie systémovej diagnostiky
    if (bootCount > 1) {
        printSystemDiagnostics();
    }

    // Kontrola zdravia pamäte
    checkMemoryHealth();

    // Kontrola konfiguračného režimu (tlačidlo stlačené pri štarte)
    delay(100);
    if (wakeupReason == ESP_SLEEP_WAKEUP_GPIO || digitalRead(TRIGGER_PIN) == LOW) {
        Serial.println("Entering configuration mode...");
        delay(500);
        WiFi.mode(WIFI_STA);

        if (!startConfigPortal()) {
            if (isRunningOnBattery()) {
                Serial.println("Config failed on battery - entering sleep");
                enterSleep(config.sleepTime);
            }
        } else {
            Serial.println("Configuration completed - continuing with normal operation");
        }
    }

    // Connect to WiFi
    if (!connectToWiFi()) {
        // Connection failed, sleep handled in connectToWiFi
        return;
    }

    // Initialize sensors
    if (!initializeSensors()) {
        Serial.println("WARNING: Sensor initialization failed");
    }

    Serial.println("Setup completed successfully");
}

void loop() {
    // Kontrola tlačidla a portálu
    checkButton();

    // Preskočenie merania ak beží portál
    if (portalRunning) {
        delay(100);  // Malé zpoždenie pre optimalizáciu
        return;
    }

    Serial.println(String('-', 50));
    Serial.println("🔄 Začínam nový cyklus merania...");

    // Kontrola WiFi pripojenia
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("📡 WiFi odpojené - pokúšam sa znovu pripojiť...");

        // Pokus o rýchle znovupripojenie
        WiFi.reconnect();
        delay(5000);

        if (WiFi.status() != WL_CONNECTED) {
            Serial.println("❌ WiFi stále odpojené - spím a skúsim na ďalšom boote");
            enterSleep(config.sleepTime);
            return;
        } else {
            Serial.println("✅ WiFi znovu pripojené");
        }
    }

    // Kontrola batérie pred meraním
    float batteryVoltage = getBatteryVoltage();
    if (isBatteryCritical()) {
        Serial.println("🚨 Kritická batéria počas merania!");
        enterEmergencyMode();
        return;
    }

    // Čítanie dát zo senzorov
    Serial.println("📊 Čítam dáta zo senzorov...");
    SensorData sensorData = readSensorData();

    // Zobrazenie nameraných dát
    Serial.println("📈 Namerané hodnoty:");
    Serial.printf("  🌡️  Teplota: %.2f°C\n", sensorData.temperature);

    if (config.sensorType <= 4 && config.sensorType != SENSOR_DS18B20) {
        Serial.printf("  💧 Vlhkosť: %.2f%%\n", sensorData.humidity);
    }

    if (config.sensorType == SENSOR_BME280_0x76 || config.sensorType == SENSOR_BME280_0x77) {
        Serial.printf("  🌪️  Tlak: %.2f hPa\n", sensorData.pressure);
    }

    if (config.sensorType == SENSOR_SCD41) {
        Serial.printf("  🫁 CO2: %d ppm\n", sensorData.co2);
    }

    Serial.printf("  🔋 Batéria: %.2fV\n", batteryVoltage);

    // Validácia napätia batérie (povoliť až 6V pre externé napájanie ako USB)
    if (isnan(batteryVoltage) || batteryVoltage < 0.0f || batteryVoltage > 6.0f) {
        Serial.printf("❌ Neplatné napätie batérie: %.2fV - preskakujem odosielanie dát\n", batteryVoltage);
        sensorData.isValid = false;
    } else {
        Serial.printf("✅ Validácia napätia batérie úspešná: %.2fV\n", batteryVoltage);
    }

    // Odoslanie dát ak sú platné
    bool sendSuccess = false;
    int successfulSends = 0;

    if (sensorData.isValid) {
        Serial.println("📤 Odosielam dáta na servery...");

        bool tmepSuccess = false;
        bool thingSpeakSuccess = false;

        // Pokus o odoslanie na TMEP
        if (config.serverSendType == SERVER_TMEP || config.serverSendType == SERVER_BOTH) {
            Serial.println("  📡 Odosielam na TMEP.cz...");
            tmepSuccess = sendToTMEP(sensorData, batteryVoltage);
            if (tmepSuccess) {
                successfulSends++;
                Serial.println("  ✅ TMEP.cz: Úspešne odoslané");
            } else {
                Serial.println("  ❌ TMEP.cz: Odosielanie zlyhalo");
            }
        }

        // Pokus o odoslanie na ThingSpeak
        if (config.serverSendType == SERVER_THINGSPEAK || config.serverSendType == SERVER_BOTH) {
            Serial.println("  📡 Odosielam na ThingSpeak...");
            thingSpeakSuccess = sendToThingSpeak(sensorData, batteryVoltage);
            if (thingSpeakSuccess) {
                successfulSends++;
                Serial.println("  ✅ ThingSpeak: Úspešne odoslané");
            } else {
                Serial.println("  ❌ ThingSpeak: Odosielanie zlyhalo");
            }
        }

        sendSuccess = tmepSuccess || thingSpeakSuccess;

        if (sendSuccess) {
            totalSuccessfulSends++;
            lastSuccessfulSendTime = millis();
            lastValidTemperature = sensorData.temperature;
            Serial.printf("🎉 Dáta úspešne odoslané na %d/%d serverov\n",
                         successfulSends,
                         (config.serverSendType == SERVER_BOTH) ? 2 : 1);
        } else {
            totalFailedSends++;
            Serial.println("💥 Zlyhalo odosielanie na všetky servery");
        }
    } else {
        Serial.println("❌ Neplatné dáta zo senzorov - preskakujem odosielanie");
    }

    // Určenie dĺžky spánku
    uint16_t sleepDuration = config.sleepTime;

    if (!sendSuccess && isRunningOnBattery()) {
        // Kratší spánok na batérii pri zlyhaní odosielania
        sleepDuration = min(sleepDuration, static_cast<uint16_t>(10));
        Serial.printf("⚠️  Odosielanie zlyhalo na batérii - kratší spánok: %d minút\n", sleepDuration);
    } else if (!sendSuccess) {
        // Kratší spánok pri externom napájaní pri zlyhaní
        sleepDuration = min(sleepDuration, static_cast<uint16_t>(5));
        Serial.printf("⚠️  Odosielanie zlyhalo - kratší spánok: %d minút\n", sleepDuration);
    }

    // Vstup do režimu spánku
    Serial.printf("😴 Vstupujem do spánku na %d minút\n", sleepDuration);
    Serial.println(String('=', 50));
    enterSleep(sleepDuration);
}
