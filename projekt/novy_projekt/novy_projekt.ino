/**
 * @file meteomini2_arduino.ino
 * @brief Professional IoT Weather Station for ESP32-C3 (Arduino IDE Compatible)
 * @version 2.0.0
 * <AUTHOR> Agent (Arduino IDE Compatible Version)
 * 
 * Features:
 * - Multi-sensor support (SHT40, BME280, SCD41, DS18B20)
 * - Dual server support (TMEP.cz, ThingSpeak)
 * - WiFi configuration portal with LED indication
 * - Deep sleep power management
 * - Battery monitoring with emergency modes
 * - Non-blocking operations
 * - Robust error handling
 * - Arduino IDE compatible

 ## Hardware

- **Základní deska:** [LaskaKit ESP32-C3 Meteo Mini](https://laskakit.cz/laskakit-esp32-c3-meteo-mini/)
- **Schéma zapojení:** [PDF Dokumentace k HW](https://github.com/LaskaKit/Meteo_Mini/blob/main/HW/LaskaKit_METEO_MINI_v_3_5.pdf)

### Zapojen<PERSON> pinů

- `LED_PIN`: 7 (<PERSON><PERSON><PERSON> LED)
- `TRIGGER_PIN`: 2 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pro spuštění konfigurace)
- `PWR_PIN`: 3 (Napájení senzorů)
- `ONEWIRE_PIN`: 10 (Sběrnice pro DS18B20)
- `DEFAULT_SDA_PIN`: 19 (I2C SDA)
- `DEFAULT_SCL_PIN`: 18 (I2C SCL)

 */

#include <WiFi.h>
#include <WiFiManager.h>
#include <HTTPClient.h>
#include <EEPROM.h>
#include <Wire.h>
#include <SensirionI2cSht4x.h>
#include <Adafruit_BME280.h>
#include <SensirionI2cScd4x.h>
#include <OneWire.h>
#include <DallasTemperature.h>
#include <esp_system.h>  // Pro esp_reset_reason()

// ============================================================================
// CONSTANTS AND CONFIGURATION
// ============================================================================

// Konfigurace hardware - OPRAVENÉ PINY PODLE DOKUMENTACE
#define LED_PIN 7                   // Pin pro stavovou LED
#define TRIGGER_PIN 2               // Pin pro tlačítko konfigurace
#define PWR_PIN 3                   // Pin pro napájení senzorů
#define ONEWIRE_PIN 10              // OPRAVENO: DS18B20 pin podle dokumentace
#define DEFAULT_SDA_PIN 19          // OPRAVENO: I2C SDA pin podle dokumentace
#define DEFAULT_SCL_PIN 18          // OPRAVENO: I2C SCL pin podle dokumentace

// Správa napájení - VYLEPŠENÉ PRAHY A LIMITY
#define BATTERY_LOW_THRESHOLD 3.2f          // ZVÝŠENO pro bezpečnost Li-Po baterií
#define EXTERNAL_POWER_THRESHOLD 4.2f       // SNÍŽENO pro lepší detekci
#define EMERGENCY_SLEEP_TIME 60              // Nouzový spánek v minutách
#define MAX_WIFI_CONNECT_ATTEMPTS 3          // SNÍŽENO pro úsporu energie
#define BATTERY_CRITICAL_THRESHOLD 2.8f     // NOVÉ: kritická úroveň baterie

// Konfigurace časování
#define LED_BLINK_INTERVAL 1000         // Interval blikání LED v ms (prodlouženo pro úsporu baterie)
#define PORTAL_AUTO_CLOSE_TIME 300000   // Automatické zavření portálu za 5 minut
#define CONFIG_TIMEOUT_BATTERY 120      // Timeout konfigurace na baterii - 2 minuty
#define CONFIG_TIMEOUT_EXTERNAL 300     // Timeout konfigurace na externí napájení - 5 minut

// Konfigurace EEPROM
#define EEPROM_SIZE 512             // Velikost EEPROM pro konfiguraci
#define CONFIG_VERSION 2            // Verze konfigurace pro kompatibilitu

// Typy senzorů - identifikátory pro různé senzory
#define SENSOR_SHT40_0x44 0         // SHT40 na I2C adrese 0x44
#define SENSOR_SHT40_0x45 1         // SHT40 na I2C adrese 0x45
#define SENSOR_BME280_0x76 2        // BME280 na I2C adrese 0x76
#define SENSOR_BME280_0x77 3        // BME280 na I2C adrese 0x77
#define SENSOR_SCD41 4              // SCD41 CO2 senzor
#define SENSOR_DS18B20 5            // DS18B20 teplotní senzor (OneWire)
#define SENSOR_AUTO_DETECT 99       // Automatická detekce senzoru

// Typy serverů pro odesílání dat
#define SERVER_NONE 0               // Neodesílat nikam
#define SERVER_TMEP 1               // Odesílat pouze na TMEP.cz
#define SERVER_THINGSPEAK 2         // Odesílat pouze na ThingSpeak
#define SERVER_BOTH 3               // Odesílat na oba servery

// ============================================================================
// STRUKTURA KONFIGURACE
// ============================================================================

/**
 * Struktura pro uložení konfigurace zařízení v EEPROM
 * Obsahuje všechna nastavení potřebná pro provoz meteostanice
 */
struct Config {
    uint8_t configVersion;          // Verze konfigurace pro kontrolu kompatibility
    char serverAddressTmep[40];     // Adresa TMEP.cz serveru
    char thingSpeakApiKey[40];      // API klíč pro ThingSpeak
    char thingSpeakChannelId[20];   // ID kanálu ThingSpeak
    uint16_t sleepTime;             // Doba spánku mezi měřeními (minuty)
    uint8_t sensorType;             // Typ použitého senzoru
    uint8_t serverSendType;         // Kam odesílat data (TMEP/ThingSpeak/oba)
    uint8_t sdaPin;                 // Pin pro I2C SDA
    uint8_t sclPin;                 // Pin pro I2C SCL
    uint8_t tsFieldTemp;            // ThingSpeak pole pro teplotu
    uint8_t tsFieldHum;             // ThingSpeak pole pro vlhkost
    uint8_t tsFieldPress;           // ThingSpeak pole pro tlak
    uint8_t tsFieldCO2;             // ThingSpeak pole pro CO2
    uint8_t tsFieldBatt;            // ThingSpeak pole pro napětí baterie
    
    /**
     * Kontrola platnosti konfigurace
     * @return true pokud je konfigurace platná
     */
    bool isValid() {
        return configVersion == CONFIG_VERSION &&
               sleepTime > 0 && sleepTime <= 200 &&
               sensorType <= 99 &&
               serverSendType <= 3;
    }

    /**
     * Nastavení výchozích hodnot konfigurace
     * Volá se při prvním spuštění nebo při neplatné konfiguraci
     */
    void setDefaults() {
        configVersion = CONFIG_VERSION;
        strcpy(serverAddressTmep, "xny3ef-8khshy.tmep.cz/index.php");
        strcpy(thingSpeakApiKey, "ABS8T4OULVYA1FOL");
        strcpy(thingSpeakChannelId, "845449");
        sleepTime = 5;                      // 5 minut mezi měřeními
        sensorType = SENSOR_AUTO_DETECT;    // Automatická detekce senzoru
        serverSendType = SERVER_BOTH;       // Odesílat na oba servery
        sdaPin = DEFAULT_SDA_PIN;           // Výchozí I2C SDA pin
        sclPin = DEFAULT_SCL_PIN;           // Výchozí I2C SCL pin
        tsFieldTemp = 2;                    // ThingSpeak pole pro teplotu
        tsFieldHum = 3;                     // ThingSpeak pole pro vlhkost
        tsFieldPress = 5;                   // ThingSpeak pole pro tlak
        tsFieldCO2 = 6;                     // ThingSpeak pole pro CO2
        tsFieldBatt = 4;                    // ThingSpeak pole pro baterii
    }
};

// ============================================================================
// GLOBÁLNÍ PROMĚNNÉ
// ============================================================================

// Konfigurace zařízení
Config config;

// Hardware objekty pro komunikaci se senzory a WiFi
WiFiManager wifiManager;                    // Správce WiFi připojení
SensirionI2cSht4x sht4x;                   // SHT40 teplotní a vlhkostní senzor
Adafruit_BME280 bme;                       // BME280 teplotní, vlhkostní a tlakový senzor
SensirionI2cScd4x scd4x;                   // SCD41 CO2 senzor
OneWire oneWire(ONEWIRE_PIN);              // OneWire sběrnice pro DS18B20
DallasTemperature ds18b20(&oneWire);       // DS18B20 teplotní senzor

// Stavové proměnné uložené v RTC paměti (přežijí deep sleep)
RTC_DATA_ATTR uint32_t bootCount = 0;              // Počet bootů zařízení
RTC_DATA_ATTR uint8_t failedConnectAttempts = 0;   // Počet neúspěšných WiFi připojení
RTC_DATA_ATTR uint32_t totalSuccessfulSends = 0;   // Celkový počet úspěšných odeslání
RTC_DATA_ATTR uint32_t totalFailedSends = 0;       // Celkový počet neúspěšných odeslání
RTC_DATA_ATTR float lastValidTemperature = NAN;    // Poslední platná teplota
RTC_DATA_ATTR uint32_t lastSuccessfulSendTime = 0; // Čas posledního úspěšného odeslání

// Stav konfiguračního portálu
volatile bool portalRunning = false;       // Běží konfigurační portál?
volatile bool ledState = false;            // Aktuální stav LED
uint32_t lastLedBlink = 0;                 // Čas posledního bliknutí LED
uint32_t portalStartTime = 0;              // Čas spuštění portálu

// Data senzorů a systémové informace
DeviceAddress sensorAddress;              // Adresa DS18B20 senzoru
const char* version = "2.0.0";            // Verze firmware

// Parametry WiFiManageru pro konfigurační portál
WiFiManagerParameter* customParams[13];   // Pole parametrů pro webové rozhraní
bool parametersInitialized = false;      // Jsou parametry inicializované?

// ============================================================================
// STRUKTURA DAT ZE SENZORŮ
// ============================================================================

/**
 * Struktura pro uložení naměřených dat ze všech typů senzorů
 * Obsahuje všechny možné hodnoty, které mohou senzory poskytovat
 */
struct SensorData {
    float temperature;      // Teplota ve °C
    float humidity;         // Relativní vlhkost v %
    float pressure;         // Atmosférický tlak v hPa
    uint16_t co2;          // Koncentrace CO2 v ppm
    bool isValid;          // Jsou data platná?

    /**
     * Konstruktor - inicializuje všechny hodnoty na výchozí
     */
    SensorData() {
        temperature = 0.0f;
        humidity = 0.0f;
        pressure = 0.0f;
        co2 = 0;
        isValid = false;
    }
    
    /**
     * Validace naměřených dat podle typu senzoru
     * Kontroluje rozumnost hodnot a jejich kombinací
     * @return true pokud jsou data platná
     */
    bool validateData() {
        // Rozšířená validace teploty s různými limity pro různé senzory
        float tempMin = -40.0f, tempMax = 85.0f;
        if (config.sensorType == SENSOR_DS18B20) {
            tempMin = -55.0f; tempMax = 125.0f;  // DS18B20 má širší rozsah
        } else if (config.sensorType == SENSOR_SCD41) {
            tempMin = -10.0f; tempMax = 60.0f;   // SCD41 má užší rozsah
        }

        if (isnan(temperature) || temperature < tempMin || temperature > tempMax) {
            Serial.printf("Neplatná teplota: %.2f°C (rozsah: %.1f až %.1f°C)\n",
                         temperature, tempMin, tempMax);
            return false;
        }

        // Validace vlhkosti (pro senzory které ji podporují)
        if (config.sensorType <= 4 && config.sensorType != SENSOR_DS18B20) {
            if (isnan(humidity) || humidity < 0.0f || humidity > 100.0f) {
                Serial.printf("Neplatná vlhkost: %.2f%% (rozsah: 0-100%%)\n", humidity);
                return false;
            }
        }

        // Validace tlaku (pouze BME280)
        if (config.sensorType == SENSOR_BME280_0x76 || config.sensorType == SENSOR_BME280_0x77) {
            if (isnan(pressure) || pressure < 300.0f || pressure > 1100.0f) {
                Serial.printf("Neplatný tlak: %.2f hPa (rozsah: 300-1100 hPa)\n", pressure);
                return false;
            }
        }

        // Rozšířená validace CO2 (pouze SCD41)
        if (config.sensorType == SENSOR_SCD41) {
            if (co2 < 400 || co2 > 40000) {  // ROZŠÍŘENÝ rozsah
                Serial.printf("Neplatné CO2: %d ppm (rozsah: 400-40000 ppm)\n", co2);
                return false;
            }
            // Varování při vysokých hodnotách
            if (co2 > 5000) {
                Serial.printf("VAROVÁNÍ: Vysoká koncentrace CO2: %d ppm\n", co2);
            }
        }

        // Kontrola rozumnosti kombinací hodnot
        if (!isnan(temperature) && !isnan(humidity)) {
            // Kontrola rosného bodu - vlhkost nemůže být 100% při vysokých teplotách
            if (temperature > 30.0f && humidity > 95.0f) {
                Serial.printf("VAROVÁNÍ: Podezřelá kombinace T=%.1f°C, RH=%.1f%%\n",
                             temperature, humidity);
            }
        }

        return true;
    }
};

// ============================================================================
// SYSTEM MONITORING AND DIAGNOSTICS FUNCTIONS
// ============================================================================

/**
 * Zobrazení systémové diagnostiky
 * Vypíše statistiky o fungování zařízení včetně důvodu posledního resetu
 * Používá ESP32-specifické funkce pro získání informací o systému
 */
void printSystemDiagnostics() {
    Serial.println("🔍 Systémová diagnostika:");
    Serial.printf("  📊 Celkem úspěšných odeslání: %d\n", totalSuccessfulSends);
    Serial.printf("  📊 Celkem neúspěšných odeslání: %d\n", totalFailedSends);
    Serial.printf("  📊 Úspěšnost: %.1f%%\n",
                 (totalSuccessfulSends + totalFailedSends > 0) ?
                 (100.0f * totalSuccessfulSends / (totalSuccessfulSends + totalFailedSends)) : 0.0f);
    Serial.printf("  💾 Volná heap paměť: %d bytes\n", ESP.getFreeHeap());
    Serial.printf("  💾 Nejmenší volná heap: %d bytes\n", ESP.getMinFreeHeap());

    // Získání důvodu resetu pro ESP32
    esp_reset_reason_t resetReason = esp_reset_reason();
    const char* resetReasonStr = "Neznámý";
    switch (resetReason) {
        case ESP_RST_POWERON: resetReasonStr = "Zapnutí napájení"; break;
        case ESP_RST_EXT: resetReasonStr = "Externí reset"; break;
        case ESP_RST_SW: resetReasonStr = "Software reset"; break;
        case ESP_RST_PANIC: resetReasonStr = "Kernel panic"; break;
        case ESP_RST_INT_WDT: resetReasonStr = "Interrupt watchdog"; break;
        case ESP_RST_TASK_WDT: resetReasonStr = "Task watchdog"; break;
        case ESP_RST_WDT: resetReasonStr = "Watchdog reset"; break;
        case ESP_RST_DEEPSLEEP: resetReasonStr = "Probuzení z deep sleep"; break;
        case ESP_RST_BROWNOUT: resetReasonStr = "Brownout reset"; break;
        case ESP_RST_SDIO: resetReasonStr = "SDIO reset"; break;
        default: resetReasonStr = "Ostatní"; break;
    }
    Serial.printf("  🔄 Důvod posledního resetu: %s (%d)\n", resetReasonStr, resetReason);

    if (!isnan(lastValidTemperature)) {
        Serial.printf("  🌡️  Poslední platná teplota: %.2f°C\n", lastValidTemperature);
    }
}

/**
 * Kontrola stavu paměti systému
 * Při kriticky nízké paměti restartuje zařízení
 */
void checkMemoryHealth() {
    uint32_t freeHeap = ESP.getFreeHeap();
    uint32_t minFreeHeap = ESP.getMinFreeHeap();

    if (freeHeap < 10000) {  // Méně než 10KB volné paměti
        Serial.printf("⚠️  VAROVÁNÍ: Nízká volná paměť: %d bytes\n", freeHeap);
    }

    if (minFreeHeap < 5000) {  // Kriticky nízká paměť
        Serial.printf("🚨 KRITICKÉ: Minimální volná paměť: %d bytes\n", minFreeHeap);
        Serial.println("🔄 Restartuji systém pro uvolnění paměti...");
        ESP.restart();
    }
}

// ============================================================================
// FUNKCE PRO SPRÁVU NAPÁJENÍ
// ============================================================================

/**
 * Měření napětí baterie s pokročilým filtrováním
 * Používá více vzorků a odstraňuje extrémní hodnoty pro přesnější měření
 * @return napětí baterie ve voltech
 */
float getBatteryVoltage() {
    const uint8_t SAMPLES = 10;  // ZVÝŠENO pro přesnější měření
    float sum = 0;
    float readings[SAMPLES];

    // Sběr vzorků s časovým rozestupem
    for (uint8_t i = 0; i < SAMPLES; i++) {
        readings[i] = analogReadMilliVolts(A0) * 1.7693877551 / 1000.0f;
        delay(20);  // ZVÝŠENO zpoždění pro stabilitu
    }

    // Odstranění extrémních hodnot (outliers) - jednoduchý bubble sort
    for (uint8_t i = 0; i < SAMPLES - 2; i++) {
        for (uint8_t j = i + 1; j < SAMPLES - 1; j++) {
            if (readings[i] > readings[j]) {
                float temp = readings[i];
                readings[i] = readings[j];
                readings[j] = temp;
            }
        }
    }

    // Průměr ze středních hodnot (bez nejvyšší a nejnižší)
    for (uint8_t i = 1; i < SAMPLES - 1; i++) {
        sum += readings[i];
    }

    float voltage = sum / (SAMPLES - 2);

    // Validace výsledku
    if (voltage < 0.0f || voltage > 6.0f) {
        Serial.printf("VAROVÁNÍ: Neplatné napětí baterie: %.2fV\n", voltage);
        return 0.0f;
    }

    return voltage;
}

/**
 * Detekce zdroje napájení
 * @return true pokud zařízení běží na baterii
 */
bool isRunningOnBattery() {
    return getBatteryVoltage() < EXTERNAL_POWER_THRESHOLD;
}

/**
 * Kontrola nízkého napětí baterie
 * @return true pokud je baterie slabá
 */
bool isBatteryLow() {
    float voltage = getBatteryVoltage();
    return voltage > 0.0f && voltage < BATTERY_LOW_THRESHOLD;
}

/**
 * Kontrola kritického napětí baterie
 * @return true pokud je baterie v kritickém stavu
 */
bool isBatteryCritical() {
    float voltage = getBatteryVoltage();
    return voltage > 0.0f && voltage < BATTERY_CRITICAL_THRESHOLD;
}

/**
 * Nouzový režim při kriticky nízké baterii
 * Vypne všechny periferie a přejde do dlouhého spánku
 */
void enterEmergencyMode() {
    float voltage = getBatteryVoltage();
    Serial.printf("NOUZOVÝ REŽIM: Kriticky nízká baterie (%.2fV < %.2fV)!\n",
                 voltage, BATTERY_CRITICAL_THRESHOLD);
    Serial.printf("Vstupuji do nouzového spánku na %d minut.\n", EMERGENCY_SLEEP_TIME);

    // Vypnutí všech periférií pro maximální úsporu energie
    digitalWrite(PWR_PIN, LOW);     // Vypnutí napájení senzorů
    WiFi.disconnect(true);          // Odpojení od WiFi
    WiFi.mode(WIFI_OFF);           // Vypnutí WiFi modulu

    // Dlouhý nouzový spánek
    esp_sleep_enable_timer_wakeup(EMERGENCY_SLEEP_TIME * 60ULL * 1000000ULL);
    esp_deep_sleep_start();
}

/**
 * Vstup do režimu hlubokého spánku
 * @param minutes počet minut spánku
 */
void enterSleep(uint16_t minutes) {
    Serial.printf("Vstupuji do spánku na %d minut\n", minutes);

    // Bezpečnostní kontrola baterie před spánkem
    if (isBatteryCritical()) {
        Serial.println("VAROVÁNÍ: Kritická úroveň baterie před spánkem!");
        enterEmergencyMode();
        return;
    }

    // Vypnutí senzorů a periférií pro úsporu energie
    digitalWrite(PWR_PIN, LOW);     // Vypnutí napájení senzorů

    // Odpojení od WiFi pro úsporu energie
    WiFi.disconnect(true);
    WiFi.mode(WIFI_OFF);

    // Konfigurace časovače pro probuzení
    esp_sleep_enable_timer_wakeup(minutes * 60ULL * 1000000ULL);

    Serial.println("Spánek nakonfigurován s časovačem");
    Serial.printf("Probuzení za %d minut\n", minutes);
    Serial.flush();  // Zajištění odeslání všech zpráv
    delay(100);

    esp_deep_sleep_start();
}

// ============================================================================
// FUNKCE PRO SPRÁVU LED
// ============================================================================

/**
 * Inicializace stavové LED
 */
void initLED() {
    pinMode(LED_PIN, OUTPUT);
    digitalWrite(LED_PIN, LOW);    // LED vypnuta

    // Test LED při startu - pouze při externím napájení pro úsporu baterie
    if (!isRunningOnBattery()) {
        Serial.printf("Test LED na pinu %d...\n", LED_PIN);
        digitalWrite(LED_PIN, HIGH);
        delay(150);  // Kratší doba pro úsporu
        digitalWrite(LED_PIN, LOW);
        Serial.println("Test LED dokončen");
    } else {
        Serial.println("Test LED přeskočen - úspora baterie");
    }
}

/**
 * Spuštění blikání LED (indikace konfiguračního režimu)
 */
void startLEDBlinking() {
    digitalWrite(LED_PIN, HIGH);
    ledState = true;
    lastLedBlink = millis();
    portalStartTime = millis();
    Serial.println("LED blikání spuštěno");
}

/**
 * Zastavení blikání LED
 */
void stopLEDBlinking() {
    digitalWrite(LED_PIN, LOW);
    ledState = false;
    Serial.println("LED blikání zastaveno");
}

/**
 * Aktualizace stavu blikající LED
 * Volá se v hlavní smyčce během běhu konfiguračního portálu
 */
void updateLEDBlink() {
    if (portalRunning && (millis() - lastLedBlink >= LED_BLINK_INTERVAL)) {
        ledState = !ledState;
        digitalWrite(LED_PIN, ledState ? HIGH : LOW);
        lastLedBlink = millis();
    }
}

// ============================================================================
// FUNKCE PRO SPRÁVU KONFIGURACE
// ============================================================================

/**
 * Uložení konfigurace do EEPROM
 * @return true při úspěšném uložení
 */
bool saveConfig() {
    EEPROM.put(0, config);
    bool success = EEPROM.commit();
    if (success) {
        Serial.println("Konfigurace uložena do EEPROM");
    } else {
        Serial.println("Chyba při ukládání konfigurace");
    }
    return success;
}

/**
 * Načtení konfigurace z EEPROM
 * @return true pokud byla načtena platná konfigurace
 */
bool loadConfig() {
    Config tempConfig;
    EEPROM.get(0, tempConfig);

    if (tempConfig.isValid()) {
        config = tempConfig;
        Serial.println("Konfigurace načtena z EEPROM");
        return true;
    } else {
        Serial.println("Neplatná konfigurace, používám výchozí hodnoty");
        config.setDefaults();
        saveConfig();
        return false;
    }
}

/**
 * Reset konfigurace na výchozí hodnoty
 */
void resetConfig() {
    config.setDefaults();
    saveConfig();
    Serial.println("Konfigurace resetována na výchozí hodnoty");
}

/**
 * Zobrazení aktuální konfigurace
 * Vypíše všechna nastavení zařízení v přehledném formátu
 */
void printConfig() {
    Serial.println("⚙️  === AKTUÁLNÍ KONFIGURACE ===");
    Serial.printf("📡 TMEP Server: %s\n", strlen(config.serverAddressTmep) > 0 ? config.serverAddressTmep : "NENAKONFIGUROVÁNO");
    Serial.printf("📊 ThingSpeak Channel: %s\n", strlen(config.thingSpeakChannelId) > 0 ? config.thingSpeakChannelId : "NENAKONFIGUROVÁNO");
    Serial.printf("😴 Doba spánku: %d minut\n", config.sleepTime);

    // Popis typu senzoru
    const char* sensorNames[] = {
        "SHT40 (0x44)", "SHT40 (0x45)", "BME280 (0x76)", "BME280 (0x77)",
        "SCD41", "DS18B20", "NEZNÁMÝ"
    };
    int sensorIndex = (config.sensorType <= 5) ? config.sensorType : 6;
    if (config.sensorType == 99) {
        Serial.println("🔍 Typ senzoru: AUTO-DETEKCE");
    } else {
        Serial.printf("🌡️  Typ senzoru: %s (%d)\n", sensorNames[sensorIndex], config.sensorType);
    }

    // Popis typu serveru
    Serial.print("📤 Odesílání dat: ");
    switch (config.serverSendType) {
        case SERVER_NONE: Serial.println("VYPNUTO"); break;
        case SERVER_TMEP: Serial.println("TMEP.cz"); break;
        case SERVER_THINGSPEAK: Serial.println("ThingSpeak"); break;
        case SERVER_BOTH: Serial.println("TMEP.cz + ThingSpeak"); break;
        default: Serial.printf("NEZNÁMÉ (%d)\n", config.serverSendType); break;
    }

    Serial.printf("🔌 I2C piny: SDA=%d, SCL=%d\n", config.sdaPin, config.sclPin);
    Serial.printf("📋 ThingSpeak pole: T=%d, H=%d, P=%d, CO2=%d, Bat=%d\n",
                 config.tsFieldTemp, config.tsFieldHum, config.tsFieldPress,
                 config.tsFieldCO2, config.tsFieldBatt);
    Serial.println(String('=', 40));
}

// ============================================================================
// SENSOR MANAGEMENT FUNCTIONS
// ============================================================================

bool initSHT40(uint8_t address) {
    sht4x.begin(Wire, address);
    uint32_t serialNumber;
    uint16_t error = sht4x.serialNumber(serialNumber);

    if (error == 0) {
        Serial.printf("SHT40 sensor found at 0x%02X\n", address);
        return true;
    }
    return false;
}

bool initBME280(uint8_t address) {
    if (bme.begin(address)) {
        Serial.printf("BME280 sensor found at 0x%02X\n", address);
        bme.setSampling(Adafruit_BME280::MODE_FORCED,
                       Adafruit_BME280::SAMPLING_X1,
                       Adafruit_BME280::SAMPLING_X1,
                       Adafruit_BME280::SAMPLING_X1,
                       Adafruit_BME280::FILTER_OFF);
        return true;
    }
    return false;
}

bool initSCD41() {
    scd4x.begin(Wire, 0x62);
    uint16_t error = scd4x.startPeriodicMeasurement();

    if (error == 0) {
        Serial.println("SCD41 sensor found");
        delay(200); // Warm-up time
        return true;
    }
    return false;
}

bool initDS18B20() {
    if (oneWire.search(sensorAddress)) {
        Serial.println("DS18B20 sensor found");
        return true;
    }
    oneWire.reset_search();
    return false;
}

bool initializeSpecificSensor(uint8_t sensorType) {
    switch (sensorType) {
        case SENSOR_SHT40_0x44:
            return initSHT40(0x44);
        case SENSOR_SHT40_0x45:
            return initSHT40(0x45);
        case SENSOR_BME280_0x76:
            return initBME280(0x76);
        case SENSOR_BME280_0x77:
            return initBME280(0x77);
        case SENSOR_SCD41:
            return initSCD41();
        case SENSOR_DS18B20:
            return initDS18B20();
        default:
            return false;
    }
}

bool autoDetectSensor() {
    Serial.println("Auto-detecting sensors...");

    // Try SHT40 0x44
    if (initializeSpecificSensor(SENSOR_SHT40_0x44)) {
        config.sensorType = SENSOR_SHT40_0x44;
        return true;
    }

    // Try SHT40 0x45
    if (initializeSpecificSensor(SENSOR_SHT40_0x45)) {
        config.sensorType = SENSOR_SHT40_0x45;
        return true;
    }

    // Try BME280 0x76
    if (initializeSpecificSensor(SENSOR_BME280_0x76)) {
        config.sensorType = SENSOR_BME280_0x76;
        return true;
    }

    // Try BME280 0x77
    if (initializeSpecificSensor(SENSOR_BME280_0x77)) {
        config.sensorType = SENSOR_BME280_0x77;
        return true;
    }

    // Try SCD41
    if (initializeSpecificSensor(SENSOR_SCD41)) {
        config.sensorType = SENSOR_SCD41;
        return true;
    }

    // Try DS18B20
    if (initializeSpecificSensor(SENSOR_DS18B20)) {
        config.sensorType = SENSOR_DS18B20;
        return true;
    }

    Serial.println("WARNING: No sensor detected!");
    return false;
}

bool initializeSensors() {
    Serial.println("Initializing sensors...");

    // Power up sensors
    pinMode(PWR_PIN, OUTPUT);
    digitalWrite(PWR_PIN, HIGH);
    delay(100);

    // Initialize I2C
    Wire.begin(config.sdaPin, config.sclPin);

    // Initialize OneWire
    ds18b20.begin();

    // Try to detect sensors
    if (config.sensorType == SENSOR_AUTO_DETECT) {
        return autoDetectSensor();
    } else {
        return initializeSpecificSensor(config.sensorType);
    }
}

bool readSHT40(SensorData& data) {
    uint16_t error = sht4x.measureHighPrecision(data.temperature, data.humidity);
    if (error != 0) {
        Serial.println("Error reading SHT40 sensor");
        return false;
    }
    return true;
}

bool readBME280(SensorData& data) {
    bme.takeForcedMeasurement();
    data.temperature = bme.readTemperature();
    data.humidity = bme.readHumidity();
    data.pressure = bme.readPressure() / 100.0f;
    return true;
}

bool readSCD41(SensorData& data) {
    uint16_t error = scd4x.readMeasurement(data.co2, data.temperature, data.humidity);
    if (error != 0) {
        Serial.println("Error reading SCD41 sensor");
        return false;
    }
    return true;
}

bool readDS18B20(SensorData& data) {
    ds18b20.requestTemperatures();
    data.temperature = ds18b20.getTempC(sensorAddress);

    if (data.temperature == DEVICE_DISCONNECTED_C) {
        Serial.println("Error reading DS18B20 sensor");
        return false;
    }
    return true;
}

SensorData readSensorData() {
    SensorData data;

    switch (config.sensorType) {
        case SENSOR_SHT40_0x44:
        case SENSOR_SHT40_0x45:
            data.isValid = readSHT40(data);
            break;
        case SENSOR_BME280_0x76:
        case SENSOR_BME280_0x77:
            data.isValid = readBME280(data);
            break;
        case SENSOR_SCD41:
            data.isValid = readSCD41(data);
            break;
        case SENSOR_DS18B20:
            data.isValid = readDS18B20(data);
            break;
        default:
            data.isValid = false;
    }

    if (data.isValid) {
        data.isValid = data.validateData();
    }

    return data;
}

// ============================================================================
// NETWORK MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Zpracování selhání WiFi připojení
 * Implementuje progresivní backoff a nouzový režim
 */
void handleConnectionFailure() {
    failedConnectAttempts++;

    Serial.printf("Selhání WiFi připojení (pokus %d/%d)\n",
                 failedConnectAttempts, MAX_WIFI_CONNECT_ATTEMPTS);

    // Diagnostika WiFi problémů
    Serial.printf("WiFi status: %d, RSSI: %d dBm\n", WiFi.status(), WiFi.RSSI());

    if (failedConnectAttempts >= MAX_WIFI_CONNECT_ATTEMPTS) {
        Serial.printf("Dosažen maximální počet pokusů (%d). Vstupuji do nouzového spánku.\n",
                     MAX_WIFI_CONNECT_ATTEMPTS);

        // Reset počítadla po dlouhém spánku
        if (failedConnectAttempts > MAX_WIFI_CONNECT_ATTEMPTS + 2) {
            failedConnectAttempts = 0;
            Serial.println("Reset počítadla neúspěšných připojení");
        }

        enterSleep(EMERGENCY_SLEEP_TIME);
        return;
    }

    // Progresivní prodlužování intervalu (exponenciální backoff)
    uint16_t baseInterval = isRunningOnBattery() ? 5 : 2;  // Kratší interval při externím napájení
    uint16_t sleepMinutes = min(static_cast<uint16_t>(baseInterval * pow(2, failedConnectAttempts - 1)),
                               static_cast<uint16_t>(30));  // Maximum 30 minut
    sleepMinutes = max(sleepMinutes, static_cast<uint16_t>(1));

    Serial.printf("Spím na %d minut před dalším pokusem o připojení.\n", sleepMinutes);
    enterSleep(sleepMinutes);
}

/**
 * Připojení k WiFi síti
 * Používá WiFiManager pro automatické připojení nebo spuštění konfiguračního portálu
 * @return true při úspěšném připojení
 */
bool connectToWiFi() {
    if (WiFi.status() == WL_CONNECTED) {
        Serial.printf("WiFi již připojeno: %s (RSSI: %d dBm)\n",
                     WiFi.SSID().c_str(), WiFi.RSSI());
        return true;
    }

    Serial.println("Pokouším se připojit k WiFi...");

    // Nastavení WiFi módu a konfigurace
    WiFi.mode(WIFI_STA);
    WiFi.setAutoReconnect(true);
    WiFi.persistent(true);

    // Adaptivní timeout na základě napájení
    uint8_t connectTimeout = isRunningOnBattery() ? 20 : 30;
    wifiManager.setConnectTimeout(connectTimeout);

    // Nastavení dodatečných parametrů pro lepší konektivitu
    wifiManager.setConnectRetries(3);
    wifiManager.setConfigPortalBlocking(false);

    Serial.printf("Timeout připojení: %d sekund\n", connectTimeout);

    if (!wifiManager.autoConnect("Laskakit Meteo Mini Config")) {
        Serial.println("Selhalo připojení k WiFi");

        // Diagnostické informace
        Serial.printf("Poslední WiFi status: %d\n", WiFi.status());
        Serial.printf("Počet uložených sítí: %d\n", WiFi.scanNetworks());

        handleConnectionFailure();
        return false;
    }

    // Úspěšné připojení - zobrazení informací
    Serial.println("✓ Úspěšně připojeno k WiFi");
    Serial.printf("  SSID: %s\n", WiFi.SSID().c_str());
    Serial.printf("  IP adresa: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("  RSSI: %d dBm\n", WiFi.RSSI());
    Serial.printf("  Kanál: %d\n", WiFi.channel());
    Serial.printf("  MAC: %s\n", WiFi.macAddress().c_str());

    // Reset počítadla neúspěšných pokusů
    failedConnectAttempts = 0;
    return true;
}

/**
 * Odeslání dat na TMEP.cz server
 * @param data naměřená data ze senzorů
 * @param batteryVoltage napětí baterie
 * @return true při úspěšném odeslání
 */
bool sendToTMEP(const SensorData& data, float batteryVoltage) {
    // Kontrola, zda má odesílat na TMEP
    if (config.serverSendType != SERVER_TMEP && config.serverSendType != SERVER_BOTH) {
        Serial.println("TMEP: Přeskakuji - není nakonfigurováno");
        return false; // Neodeslané, protože není nakonfigurováno
    }

    if (strlen(config.serverAddressTmep) == 0) {
        Serial.println("TMEP: Prázdná adresa serveru");
        return false;
    }

    HTTPClient http;
    char url[384];  // ZVÝŠENÁ veľkosť bufferu

    // Zostavenie základnej URL s teplotou a RSSI
    int urlLen = snprintf(url, sizeof(url), "http://%s/?temp=%.2f&rssi=%d",
                         config.serverAddressTmep, data.temperature, WiFi.RSSI());

    // Kontrola pretečenia bufferu
    if (urlLen >= sizeof(url) - 50) {
        Serial.println("TMEP: URL príliš dlhá");
        return false;
    }

    // Pridanie dodatočných parametrov podľa typu senzora
    if (config.sensorType <= 4 && config.sensorType != SENSOR_DS18B20) {
        urlLen += snprintf(url + urlLen, sizeof(url) - urlLen, "&humV=%.2f", data.humidity);
    }

    if (config.sensorType == SENSOR_BME280_0x76 || config.sensorType == SENSOR_BME280_0x77) {
        urlLen += snprintf(url + urlLen, sizeof(url) - urlLen, "&pressV=%.2f", data.pressure);
    }

    if (config.sensorType == SENSOR_SCD41) {
        urlLen += snprintf(url + urlLen, sizeof(url) - urlLen, "&CO2=%d", data.co2);
    }

    // Pridanie napätia batérie
    urlLen += snprintf(url + urlLen, sizeof(url) - urlLen, "&voltage=%.2f", batteryVoltage);

    // Nastavenie HTTP timeoutov
    http.setTimeout(10000);  // 10 sekúnd timeout
    http.setConnectTimeout(5000);  // 5 sekúnd connect timeout

    if (!http.begin(url)) {
        Serial.println("TMEP: Chyba pri inicializácii HTTP klienta");
        return false;
    }

    Serial.printf("TMEP URL: %s\n", url);

    int httpCode = http.GET();
    String response = "";

    if (httpCode > 0) {
        response = http.getString();
        Serial.printf("TMEP HTTP odpoveď: %d\n", httpCode);
        if (httpCode == 200) {
            Serial.printf("TMEP odpoveď: %s\n", response.c_str());
        }
    } else {
        Serial.printf("TMEP HTTP chyba: %s\n", http.errorToString(httpCode).c_str());
    }

    http.end();
    return (httpCode == 200);
}

/**
 * Odeslání dat na ThingSpeak server
 * @param data naměřená data ze senzorů
 * @param batteryVoltage napětí baterie
 * @return true při úspěšném odeslání
 */
bool sendToThingSpeak(const SensorData& data, float batteryVoltage) {
    // Kontrola, zda má odesílat na ThingSpeak
    if (config.serverSendType != SERVER_THINGSPEAK && config.serverSendType != SERVER_BOTH) {
        Serial.println("ThingSpeak: Přeskakuji - není nakonfigurováno");
        return false; // Neodeslané, protože není nakonfigurováno
    }

    if (strlen(config.thingSpeakApiKey) == 0 || strlen(config.thingSpeakChannelId) == 0) {
        Serial.println("ThingSpeak: Chybí API klíč nebo Channel ID");
        return false;
    }

    HTTPClient http;
    const char* url = "http://api.thingspeak.com/update";
    char postData[768];  // ZVÝŠENÁ veľkosť bufferu

    // Zostavenie základných POST dát s API kľúčom a teplotou
    int dataLen = snprintf(postData, sizeof(postData), "api_key=%s&field%d=%.2f",
                          config.thingSpeakApiKey, config.tsFieldTemp, data.temperature);

    // Kontrola pretečenia bufferu
    if (dataLen >= sizeof(postData) - 100) {
        Serial.println("ThingSpeak: POST dáta príliš dlhé");
        return false;
    }

    // Pridanie vlhkosti pre podporované senzory
    if (config.sensorType <= 4 && config.sensorType != SENSOR_DS18B20) {
        dataLen += snprintf(postData + dataLen, sizeof(postData) - dataLen,
                           "&field%d=%.2f", config.tsFieldHum, data.humidity);
    }

    // Pridanie tlaku pre BME280
    if (config.sensorType == SENSOR_BME280_0x76 || config.sensorType == SENSOR_BME280_0x77) {
        dataLen += snprintf(postData + dataLen, sizeof(postData) - dataLen,
                           "&field%d=%.2f", config.tsFieldPress, data.pressure);
    }

    // Pridanie CO2 pre SCD41
    if (config.sensorType == SENSOR_SCD41) {
        dataLen += snprintf(postData + dataLen, sizeof(postData) - dataLen,
                           "&field%d=%d", config.tsFieldCO2, data.co2);
    }

    // Pridanie napätia batérie
    dataLen += snprintf(postData + dataLen, sizeof(postData) - dataLen,
                       "&field%d=%.2f", config.tsFieldBatt, batteryVoltage);

    // Nastavenie HTTP timeoutov
    http.setTimeout(15000);  // 15 sekúnd timeout (ThingSpeak môže byť pomalší)
    http.setConnectTimeout(5000);  // 5 sekúnd connect timeout

    if (!http.begin(url)) {
        Serial.println("ThingSpeak: Chyba pri inicializácii HTTP klienta");
        return false;
    }

    http.addHeader("Content-Type", "application/x-www-form-urlencoded");
    http.addHeader("User-Agent", "LaskakitMeteoMini/2.0");

    Serial.printf("ThingSpeak POST dáta: %s\n", postData);

    int httpCode = http.POST(postData);
    String response = "";

    if (httpCode > 0) {
        response = http.getString();
        Serial.printf("ThingSpeak HTTP odpoveď: %d\n", httpCode);
        if (httpCode == 200) {
            Serial.printf("ThingSpeak entry ID: %s\n", response.c_str());
            // Kontrola či je odpoveď platné číslo (entry ID)
            if (response.toInt() > 0) {
                Serial.println("✓ ThingSpeak: Dáta úspešne odoslané");
            } else {
                Serial.printf("ThingSpeak: Neočakávaná odpoveď: %s\n", response.c_str());
            }
        }
    } else {
        Serial.printf("ThingSpeak HTTP chyba: %s\n", http.errorToString(httpCode).c_str());
    }

    http.end();
    return (httpCode == 200 && response.toInt() > 0);
}

// ============================================================================
// WIFI CONFIGURATION FUNCTIONS
// ============================================================================

void saveConfigFromParameters() {
    // Update config from parameters
    strcpy(config.serverAddressTmep, customParams[0]->getValue());
    strcpy(config.thingSpeakApiKey, customParams[1]->getValue());
    strcpy(config.thingSpeakChannelId, customParams[2]->getValue());
    config.sleepTime = atoi(customParams[3]->getValue());
    config.sensorType = atoi(customParams[4]->getValue());
    config.serverSendType = atoi(customParams[5]->getValue());
    config.sdaPin = atoi(customParams[6]->getValue());
    config.sclPin = atoi(customParams[7]->getValue());
    config.tsFieldTemp = atoi(customParams[8]->getValue());
    config.tsFieldHum = atoi(customParams[9]->getValue());
    config.tsFieldPress = atoi(customParams[10]->getValue());
    config.tsFieldCO2 = atoi(customParams[11]->getValue());
    config.tsFieldBatt = atoi(customParams[12]->getValue());

    // Validate and save
    if (!config.isValid()) {
        Serial.println("Invalid configuration received, using defaults");
        config.setDefaults();
    }

    saveConfig();
}

// Static buffers for parameter values to avoid String objects
static char sleepTimeStr[8];
static char sensorTypeStr[4];
static char serverTypeStr[4];
static char sdaPinStr[4];
static char sclPinStr[4];
static char tsFieldTempStr[4];
static char tsFieldHumStr[4];
static char tsFieldPressStr[4];
static char tsFieldCO2Str[4];
static char tsFieldBattStr[4];

void initializeParameters() {
    if (parametersInitialized) return;

    // Convert numeric values to strings using snprintf
    snprintf(sleepTimeStr, sizeof(sleepTimeStr), "%d", config.sleepTime);
    snprintf(sensorTypeStr, sizeof(sensorTypeStr), "%d", config.sensorType);
    snprintf(serverTypeStr, sizeof(serverTypeStr), "%d", config.serverSendType);
    snprintf(sdaPinStr, sizeof(sdaPinStr), "%d", config.sdaPin);
    snprintf(sclPinStr, sizeof(sclPinStr), "%d", config.sclPin);
    snprintf(tsFieldTempStr, sizeof(tsFieldTempStr), "%d", config.tsFieldTemp);
    snprintf(tsFieldHumStr, sizeof(tsFieldHumStr), "%d", config.tsFieldHum);
    snprintf(tsFieldPressStr, sizeof(tsFieldPressStr), "%d", config.tsFieldPress);
    snprintf(tsFieldCO2Str, sizeof(tsFieldCO2Str), "%d", config.tsFieldCO2);
    snprintf(tsFieldBattStr, sizeof(tsFieldBattStr), "%d", config.tsFieldBatt);

    customParams[0] = new WiFiManagerParameter("tmepServer", "TMEP.CZ address", config.serverAddressTmep, 40);
    customParams[1] = new WiFiManagerParameter("thingSpeakKey", "ThingSpeak API Key", config.thingSpeakApiKey, 40);
    customParams[2] = new WiFiManagerParameter("thingSpeakChannel", "ThingSpeak Channel ID", config.thingSpeakChannelId, 20);
    customParams[3] = new WiFiManagerParameter("sleepTime", "Sleep time (minutes)", sleepTimeStr, 6);
    customParams[4] = new WiFiManagerParameter("sensorType", "Sensor type (99=auto)", sensorTypeStr, 2);
    customParams[5] = new WiFiManagerParameter("serverType", "Server type (0=None,1=TMEP,2=ThingSpeak,3=Both)", serverTypeStr, 2);
    customParams[6] = new WiFiManagerParameter("sdaPin", "I2C SDA pin", sdaPinStr, 3);
    customParams[7] = new WiFiManagerParameter("sclPin", "I2C SCL pin", sclPinStr, 3);
    customParams[8] = new WiFiManagerParameter("tsFieldTemp", "ThingSpeak field - Temperature", tsFieldTempStr, 2);
    customParams[9] = new WiFiManagerParameter("tsFieldHum", "ThingSpeak field - Humidity", tsFieldHumStr, 2);
    customParams[10] = new WiFiManagerParameter("tsFieldPress", "ThingSpeak field - Pressure", tsFieldPressStr, 2);
    customParams[11] = new WiFiManagerParameter("tsFieldCO2", "ThingSpeak field - CO2", tsFieldCO2Str, 2);
    customParams[12] = new WiFiManagerParameter("tsFieldBatt", "ThingSpeak field - Battery", tsFieldBattStr, 2);

    parametersInitialized = true;
}

void setupWiFiManager() {
    initializeParameters();

    // Add all parameters
    for (int i = 0; i < 13; i++) {
        wifiManager.addParameter(customParams[i]);
    }

    // Configure WiFiManager
    wifiManager.setSaveConfigCallback([]() {
        Serial.println("Configuration saved via WiFiManager");
        saveConfigFromParameters();
    });

    wifiManager.setAPCallback([](WiFiManager* wm) {
        Serial.println("Entered config mode");
        startLEDBlinking();
        portalRunning = true;
    });

    wifiManager.setWebServerCallback([]() {
        updateLEDBlink();
    });

    wifiManager.setParamsPage(true);
    wifiManager.setBreakAfterConfig(true);
    wifiManager.setDarkMode(true);
    wifiManager.setTitle("Laskakit Meteo Mini - Configuration");
    wifiManager.setHostname("LaskakitMeteoMini");
    wifiManager.setShowPassword(true);
    wifiManager.setHttpPort(80);
    wifiManager.setWiFiAPChannel(1);

    // Set timeout based on power source
    uint16_t timeout = isRunningOnBattery() ?
                      CONFIG_TIMEOUT_BATTERY : CONFIG_TIMEOUT_EXTERNAL;
    wifiManager.setConfigPortalTimeout(timeout);

    // Custom menu - using array instead of vector for better memory management
    const char* menu[] = {"wifi", "param", "info", "sep", "restart", "sep", "erase", "exit"};
    wifiManager.setMenu(menu, 8);
}

bool startConfigPortal() {
    setupWiFiManager();
    startLEDBlinking();
    portalRunning = true;

    bool result = wifiManager.startConfigPortal("Laskakit Meteo Mini Config");

    portalRunning = false;
    stopLEDBlinking();

    if (result) {
        Serial.println("WiFi configuration completed successfully");
    } else {
        Serial.println("WiFi configuration failed or timed out");
    }

    return result;
}

// ============================================================================
// BUTTON HANDLER FUNCTIONS
// ============================================================================

void checkButton() {
    // Handle portal if running
    if (portalRunning) {
        wifiManager.process();
        updateLEDBlink();

        // Auto-close portal after timeout
        if (millis() - portalStartTime > PORTAL_AUTO_CLOSE_TIME) {
            Serial.println("Portal auto-close timeout reached");
            wifiManager.stopWebPortal();
            portalRunning = false;
            stopLEDBlinking();
        }
    }

    // Check button press
    if (digitalRead(TRIGGER_PIN) == LOW) {
        delay(50); // Debounce
        if (digitalRead(TRIGGER_PIN) == LOW) {
            if (!portalRunning) {
                Serial.println("Button pressed - starting config portal");
                if (!startConfigPortal() && isRunningOnBattery()) {
                    enterSleep(config.sleepTime);
                }
            } else {
                Serial.println("Button pressed - stopping config portal");
                wifiManager.stopWebPortal();
                portalRunning = false;
                stopLEDBlinking();
            }
        }
    }
}

// ============================================================================
// MAIN FUNCTIONS
// ============================================================================

/**
 * Inicializační funkce - spouští se při startu zařízení
 * Provádí inicializaci hardware, načtení konfigurace a připojení k WiFi
 */
void setup() {
    Serial.begin(115200);
    delay(200);  // ZVÝŠENÉ zpoždění pro stabilizaci

    Serial.println("\n" + String('=', 60));
    Serial.println("🌡️  Laskakit Meteo Mini v2.0 (Arduino Compatible)");
    Serial.println(String('=', 60));

    // Inicializácia hardvéru
    initLED();
    pinMode(TRIGGER_PIN, INPUT_PULLUP);

    // Diagnostické informácie o systéme
    bootCount++;
    esp_sleep_wakeup_cause_t wakeupReason = esp_sleep_get_wakeup_cause();
    float batteryVoltage = getBatteryVoltage();

    Serial.printf("📊 Boot count: %d\n", bootCount);
    Serial.printf("🔋 Napětí baterie: %.2fV\n", batteryVoltage);
    Serial.printf("⚡ Zdroj napájení: %s\n", isRunningOnBattery() ? "Baterie" : "Externí");
    Serial.printf("💾 Volná heap paměť: %d bytes\n", ESP.getFreeHeap());
    Serial.printf("🔧 ESP32 Chip ID: %08X\n", (uint32_t)ESP.getEfuseMac());

    // Diagnostika důvodu probuzení
    switch (wakeupReason) {
        case ESP_SLEEP_WAKEUP_TIMER:
            Serial.println("⏰ Probuzení: Časovač");
            break;
        case ESP_SLEEP_WAKEUP_GPIO:
            Serial.println("🔘 Probuzení: Tlačítko (GPIO)");
            break;
        case ESP_SLEEP_WAKEUP_EXT0:
            Serial.println("� Probuzení: Tlačítko (EXT0) - vstupuji do konfiguračního režimu");
            break;
        case ESP_SLEEP_WAKEUP_EXT1:
            Serial.println("📡 Probuzení: Externí signál 1");
            break;
        case ESP_SLEEP_WAKEUP_TOUCHPAD:
            Serial.println("👆 Probuzení: Dotykový senzor");
            break;
        case ESP_SLEEP_WAKEUP_ULP:
            Serial.println("🔬 Probuzení: ULP koprocesor");
            break;
        default:
            Serial.println("🔌 Probuzení: Zapnutí nebo reset");
            break;
    }

    // Kontrola kritické úrovně baterie
    if (isBatteryCritical()) {
        Serial.println("🚨 KRITICKÁ ÚROVEŇ BATERIE!");
        enterEmergencyMode();
        return;
    }

    // Varování při nízké baterii
    if (isBatteryLow()) {
        Serial.println("⚠️  VAROVÁNÍ: Nízká úroveň baterie!");
    }

    // Inicializace EEPROM a načtení konfigurace
    Serial.println("📝 Inicializace EEPROM...");
    EEPROM.begin(EEPROM_SIZE);

    if (loadConfig()) {
        Serial.println("✅ Konfigurace načtena z EEPROM");
    } else {
        Serial.println("⚠️  Použita výchozí konfigurace");
    }

    printConfig();

    // Zobrazení systémové diagnostiky
    if (bootCount > 1) {
        printSystemDiagnostics();
    }

    // Kontrola zdraví paměti
    checkMemoryHealth();

    // Kontrola konfiguračního režimu (tlačítko stlačené při startu)
    delay(100);
    if (wakeupReason == ESP_SLEEP_WAKEUP_GPIO || digitalRead(TRIGGER_PIN) == LOW) {
        Serial.println("Vstupuji do konfiguračního režimu...");
        delay(500);
        WiFi.mode(WIFI_STA);

        if (!startConfigPortal()) {
            if (isRunningOnBattery()) {
                Serial.println("Konfigurace selhala na baterii - vstupuji do spánku");
                enterSleep(config.sleepTime);
            }
        } else {
            Serial.println("Konfigurace dokončena - pokračuji v normálním provozu");
        }
    }

    // Připojení k WiFi
    if (!connectToWiFi()) {
        // Selhání připojení, spánek je řešen v connectToWiFi
        return;
    }

    // Inicializace senzorů
    if (!initializeSensors()) {
        Serial.println("VAROVÁNÍ: Inicializace senzorů selhala");
    }

    Serial.println("Setup úspěšně dokončen");
}

/**
 * Hlavní smyčka programu
 * Provádí měření, odesílání dat a správu spánku
 */
void loop() {
    // Kontrola tlačítka a portálu
    checkButton();

    // Přeskočení měření pokud běží portál
    if (portalRunning) {
        delay(100);  // Malé zpoždění pro optimalizaci
        return;
    }

    Serial.println(String('-', 50));
    Serial.println("🔄 Začínám nový cyklus měření...");

    // Kontrola zdraví systému
    checkMemoryHealth();

    // Kontrola WiFi připojení
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("📡 WiFi odpojeno - pokouším se znovu připojit...");

        // Pokus o rychlé znovupřipojení
        WiFi.reconnect();
        delay(5000);

        if (WiFi.status() != WL_CONNECTED) {
            Serial.println("❌ WiFi stále odpojeno - spím a zkusím na dalším bootu");
            enterSleep(config.sleepTime);
            return;
        } else {
            Serial.println("✅ WiFi znovu připojeno");
        }
    }

    // Kontrola baterie před měřením
    float batteryVoltage = getBatteryVoltage();
    if (isBatteryCritical()) {
        Serial.println("🚨 Kritická baterie během měření!");
        enterEmergencyMode();
        return;
    }

    // Čtení dat ze senzorů
    Serial.println("📊 Čtu data ze senzorů...");
    SensorData sensorData = readSensorData();

    // Zobrazení naměřených dat
    Serial.println("📈 Naměřené hodnoty:");
    Serial.printf("  🌡️  Teplota: %.2f°C\n", sensorData.temperature);

    if (config.sensorType <= 4 && config.sensorType != SENSOR_DS18B20) {
        Serial.printf("  💧 Vlhkost: %.2f%%\n", sensorData.humidity);
    }

    if (config.sensorType == SENSOR_BME280_0x76 || config.sensorType == SENSOR_BME280_0x77) {
        Serial.printf("  🌪️  Tlak: %.2f hPa\n", sensorData.pressure);
    }

    if (config.sensorType == SENSOR_SCD41) {
        Serial.printf("  🫁 CO2: %d ppm\n", sensorData.co2);
    }

    Serial.printf("  🔋 Baterie: %.2fV\n", batteryVoltage);

    // Validace napětí baterie (povolit až 6V pro externí napájení jako USB)
    if (isnan(batteryVoltage) || batteryVoltage < 0.0f || batteryVoltage > 6.0f) {
        Serial.printf("❌ Neplatné napětí baterie: %.2fV - přeskakuji odesílání dat\n", batteryVoltage);
        sensorData.isValid = false;
    } else {
        Serial.printf("✅ Validace napětí baterie úspěšná: %.2fV\n", batteryVoltage);
    }

    // Odesílání dat pokud jsou platná
    bool sendSuccess = false;
    int successfulSends = 0;

    if (sensorData.isValid) {
        Serial.println("📤 Odesílám data na servery...");

        bool tmepSuccess = false;
        bool thingSpeakSuccess = false;

        // Pokus o odeslání na TMEP
        if (config.serverSendType == SERVER_TMEP || config.serverSendType == SERVER_BOTH) {
            Serial.println("  📡 Odesílám na TMEP.cz...");
            tmepSuccess = sendToTMEP(sensorData, batteryVoltage);
            if (tmepSuccess) {
                successfulSends++;
                Serial.println("  ✅ TMEP.cz: Úspěšně odesláno");
            } else {
                Serial.println("  ❌ TMEP.cz: Odesílání selhalo");
            }
        }

        // Pokus o odeslání na ThingSpeak
        if (config.serverSendType == SERVER_THINGSPEAK || config.serverSendType == SERVER_BOTH) {
            Serial.println("  📡 Odesílám na ThingSpeak...");
            thingSpeakSuccess = sendToThingSpeak(sensorData, batteryVoltage);
            if (thingSpeakSuccess) {
                successfulSends++;
                Serial.println("  ✅ ThingSpeak: Úspěšně odesláno");
            } else {
                Serial.println("  ❌ ThingSpeak: Odesílání selhalo");
            }
        }

        sendSuccess = tmepSuccess || thingSpeakSuccess;

        if (sendSuccess) {
            totalSuccessfulSends++;
            lastSuccessfulSendTime = millis();
            lastValidTemperature = sensorData.temperature;
            Serial.printf("🎉 Data úspěšně odeslána na %d/%d serverů\n",
                         successfulSends,
                         (config.serverSendType == SERVER_BOTH) ? 2 : 1);
        } else {
            totalFailedSends++;
            Serial.println("💥 Selhalo odesílání na všechny servery");
        }
    } else {
        Serial.println("❌ Neplatná data ze senzorů - přeskakuji odesílání");
    }

    // Určení délky spánku
    uint16_t sleepDuration = config.sleepTime;

    if (!sendSuccess && isRunningOnBattery()) {
        // Kratší spánek na baterii při selhání odesílání
        sleepDuration = min(sleepDuration, static_cast<uint16_t>(10));
        Serial.printf("⚠️  Odesílání selhalo na baterii - kratší spánek: %d minut\n", sleepDuration);
    } else if (!sendSuccess) {
        // Kratší spánek při externím napájení při selhání
        sleepDuration = min(sleepDuration, static_cast<uint16_t>(5));
        Serial.printf("⚠️  Odesílání selhalo - kratší spánek: %d minut\n", sleepDuration);
    }

    // Vstup do režimu spánku
    Serial.printf("😴 Vstupuji do spánku na %d minut\n", sleepDuration);
    Serial.println(String('=', 50));
    enterSleep(sleepDuration);
}
