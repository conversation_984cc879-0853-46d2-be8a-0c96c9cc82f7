/**
 * @file meteomini2_arduino.ino
 * @brief Professional IoT Weather Station for ESP32-C3 (Arduino IDE Compatible)
 * @version 2.0.0
 * <AUTHOR> Agent (Arduino IDE Compatible Version)
 * 
 * Features:
 * - Multi-sensor support (SHT40, BME280, SCD41, DS18B20)
 * - Dual server support (TMEP.cz, ThingSpeak)
 * - WiFi configuration portal with LED indication
 * - Deep sleep power management
 * - Battery monitoring with emergency modes
 * - Non-blocking operations
 * - Robust error handling
 * - Arduino IDE compatible
 */

#include <WiFi.h>
#include <WiFiManager.h>
#include <HTTPClient.h>
#include <EEPROM.h>
#include <Wire.h>
#include <SensirionI2cSht4x.h>
#include <Adafruit_BME280.h>
#include <SensirionI2cScd4x.h>
#include <OneWire.h>
#include <DallasTemperature.h>

// ============================================================================
// CONSTANTS AND CONFIGURATION
// ============================================================================

// Hardware Configuration
#define LED_PIN 7
#define TRIGGER_PIN 2
#define PWR_PIN 3
#define ONEWIRE_PIN 4
#define DEFAULT_SDA_PIN 8
#define DEFAULT_SCL_PIN 10 

// Power Management
#define BATTERY_LOW_THRESHOLD 3.0f
#define EXTERNAL_POWER_THRESHOLD 4.5f
#define EMERGENCY_SLEEP_TIME 60 // minutes
#define MAX_WIFI_CONNECT_ATTEMPTS 5

// Timing Configuration
#define LED_BLINK_INTERVAL 500
#define PORTAL_AUTO_CLOSE_TIME 300000 // 5 minutes
#define CONFIG_TIMEOUT_BATTERY 120    // 2 minutes
#define CONFIG_TIMEOUT_EXTERNAL 300   // 5 minutes

// EEPROM Configuration
#define EEPROM_SIZE 512
#define CONFIG_VERSION 2

// Sensor Types
#define SENSOR_SHT40_0x44 0
#define SENSOR_SHT40_0x45 1
#define SENSOR_BME280_0x76 2
#define SENSOR_BME280_0x77 3
#define SENSOR_SCD41 4
#define SENSOR_DS18B20 5
#define SENSOR_AUTO_DETECT 99

// Server Types
#define SERVER_NONE 0
#define SERVER_TMEP 1
#define SERVER_THINGSPEAK 2
#define SERVER_BOTH 3

// ============================================================================
// CONFIGURATION STRUCTURE
// ============================================================================

struct Config {
    uint8_t configVersion;
    char serverAddressTmep[40];
    char thingSpeakApiKey[40];
    char thingSpeakChannelId[20];
    uint16_t sleepTime;
    uint8_t sensorType;
    uint8_t serverSendType;
    uint8_t sdaPin;
    uint8_t sclPin;
    uint8_t tsFieldTemp;
    uint8_t tsFieldHum;
    uint8_t tsFieldPress;
    uint8_t tsFieldCO2;
    uint8_t tsFieldBatt;
    
    bool isValid() {
        return configVersion == CONFIG_VERSION &&
               sleepTime > 0 && sleepTime <= 200 &&
               sensorType <= 99 &&
               serverSendType <= 3;
    }
    
    void setDefaults() {
        configVersion = CONFIG_VERSION;
        strcpy(serverAddressTmep, "xny3ef-8khshy.tmep.cz/index.php");
        strcpy(thingSpeakApiKey, "ABS8T4OULVYA1FOL");
        strcpy(thingSpeakChannelId, "845449");
        sleepTime = 5;
        sensorType = SENSOR_AUTO_DETECT;
        serverSendType = SERVER_BOTH;
        sdaPin = DEFAULT_SDA_PIN;
        sclPin = DEFAULT_SCL_PIN;
        tsFieldTemp = 2;
        tsFieldHum = 3;
        tsFieldPress = 5;
        tsFieldCO2 = 6;
        tsFieldBatt = 4;
    }
};

// ============================================================================
// GLOBAL VARIABLES
// ============================================================================

// Configuration
Config config;

// Hardware Objects
WiFiManager wifiManager;
SensirionI2cSht4x sht4x;
Adafruit_BME280 bme;
SensirionI2cScd4x scd4x;
OneWire oneWire(ONEWIRE_PIN);
DallasTemperature ds18b20(&oneWire);

// State Variables
RTC_DATA_ATTR uint32_t bootCount = 0;
RTC_DATA_ATTR uint8_t failedConnectAttempts = 0;

// Portal State
volatile bool portalRunning = false;
volatile bool ledState = false;
uint32_t lastLedBlink = 0;
uint32_t portalStartTime = 0;

// Sensor Data
DeviceAddress sensorAddress;
const char* version = "2.0.0";

// WiFiManager Parameters
WiFiManagerParameter* customParams[13];
bool parametersInitialized = false;

// ============================================================================
// SENSOR DATA STRUCTURE
// ============================================================================

struct SensorData {
    float temperature;
    float humidity;
    float pressure;
    uint16_t co2;
    bool isValid;
    
    SensorData() {
        temperature = 0.0f;
        humidity = 0.0f;
        pressure = 0.0f;
        co2 = 0;
        isValid = false;
    }
    
    bool validateData() {
        // Temperature validation
        if (isnan(temperature) || temperature < -40.0f || temperature > 85.0f) {
            Serial.printf("Invalid temperature: %.2f\n", temperature);
            return false;
        }
        
        // Humidity validation (for sensors that support it)
        if (config.sensorType <= 4) {
            if (isnan(humidity) || humidity < 0.0f || humidity > 100.0f) {
                Serial.printf("Invalid humidity: %.2f\n", humidity);
                return false;
            }
        }
        
        // Pressure validation (BME280 only)
        if (config.sensorType == SENSOR_BME280_0x76 || config.sensorType == SENSOR_BME280_0x77) {
            if (isnan(pressure) || pressure < 300.0f || pressure > 1100.0f) {
                Serial.printf("Invalid pressure: %.2f\n", pressure);
                return false;
            }
        }
        
        // CO2 validation (SCD41 only)
        if (config.sensorType == SENSOR_SCD41) {
            if (co2 < 400 || co2 > 5000) {
                Serial.printf("Invalid CO2: %d\n", co2);
                return false;
            }
        }
        
        return true;
    }
};

// ============================================================================
// POWER MANAGEMENT FUNCTIONS
// ============================================================================

float getBatteryVoltage() {
    const uint8_t SAMPLES = 5;
    float sum = 0;

    for (uint8_t i = 0; i < SAMPLES; i++) {
        // Use analogReadMilliVolts for more accurate reading
        sum += analogReadMilliVolts(A0) * 1.7693877551 / 1000.0f;
        delay(10);
    }

    float voltage = sum / SAMPLES;
    return voltage;
}

bool isRunningOnBattery() {
    return getBatteryVoltage() < EXTERNAL_POWER_THRESHOLD;
}

bool isBatteryLow() {
    return getBatteryVoltage() < BATTERY_LOW_THRESHOLD;
}

void enterEmergencyMode() {
    Serial.printf("EMERGENCY: Low battery (%.2fV < %.2fV)!\n",
                 getBatteryVoltage(), BATTERY_LOW_THRESHOLD);
    Serial.printf("Entering emergency sleep for %d minutes.\n", EMERGENCY_SLEEP_TIME);
    
    esp_sleep_enable_timer_wakeup(EMERGENCY_SLEEP_TIME * 60ULL * 1000000ULL);
    esp_deep_sleep_start();
}

void enterSleep(uint16_t minutes) {
    Serial.printf("Entering sleep for %d minutes\n", minutes);

    // Power down sensors
    digitalWrite(PWR_PIN, LOW);

    // Configure timer wakeup
    esp_sleep_enable_timer_wakeup(minutes * 60ULL * 1000000ULL);

    // Nastavíme probuzení při LOW úrovni (když je tlačítko stisknuto)
    // 1ULL << TRIGGER_PIN vytvoří bitovou masku pro pin 9
    esp_deep_sleep_enable_gpio_wakeup(1ULL << TRIGGER_PIN, ESP_GPIO_WAKEUP_GPIO_LOW);

    Serial.println("Sleep configured with timer and GPIO wakeup");
    Serial.printf("GPIO wakeup enabled on pin %d (LOW level)\n", TRIGGER_PIN);

    esp_deep_sleep_start();
}

// ============================================================================
// LED MANAGEMENT FUNCTIONS
// ============================================================================

void initLED() {
    pinMode(LED_PIN, OUTPUT);
    digitalWrite(LED_PIN, LOW);
}

void startLEDBlinking() {
    digitalWrite(LED_PIN, HIGH);
    ledState = true;
    lastLedBlink = millis();
    portalStartTime = millis();
    Serial.println("LED blinking started");
}

void stopLEDBlinking() {
    digitalWrite(LED_PIN, LOW);
    ledState = false;
    Serial.println("LED blinking stopped");
}

void updateLEDBlink() {
    if (portalRunning && (millis() - lastLedBlink >= LED_BLINK_INTERVAL)) {
        ledState = !ledState;
        digitalWrite(LED_PIN, ledState ? HIGH : LOW);
        lastLedBlink = millis();
    }
}

// ============================================================================
// CONFIGURATION MANAGEMENT FUNCTIONS
// ============================================================================

bool saveConfig() {
    EEPROM.put(0, config);
    bool success = EEPROM.commit();
    if (success) {
        Serial.println("Configuration saved to EEPROM");
    } else {
        Serial.println("Failed to save configuration");
    }
    return success;
}

bool loadConfig() {
    Config tempConfig;
    EEPROM.get(0, tempConfig);
    
    if (tempConfig.isValid()) {
        config = tempConfig;
        Serial.println("Configuration loaded from EEPROM");
        return true;
    } else {
        Serial.println("Invalid configuration, using defaults");
        config.setDefaults();
        saveConfig();
        return false;
    }
}

void resetConfig() {
    config.setDefaults();
    saveConfig();
    Serial.println("Configuration reset to defaults");
}

void printConfig() {
    Serial.println("=== Current Configuration ===");
    Serial.printf("TMEP Server: %s\n", config.serverAddressTmep);
    Serial.printf("ThingSpeak Channel: %s\n", config.thingSpeakChannelId);
    Serial.printf("Sleep Time: %d minutes\n", config.sleepTime);
    Serial.printf("Sensor Type: %d\n", config.sensorType);
    Serial.printf("Server Type: %d\n", config.serverSendType);
    Serial.printf("I2C Pins: SDA=%d, SCL=%d\n", config.sdaPin, config.sclPin);
    Serial.println("=============================");
}

// ============================================================================
// SENSOR MANAGEMENT FUNCTIONS
// ============================================================================

bool initSHT40(uint8_t address) {
    sht4x.begin(Wire, address);
    uint32_t serialNumber;
    uint16_t error = sht4x.serialNumber(serialNumber);

    if (error == 0) {
        Serial.printf("SHT40 sensor found at 0x%02X\n", address);
        return true;
    }
    return false;
}

bool initBME280(uint8_t address) {
    if (bme.begin(address)) {
        Serial.printf("BME280 sensor found at 0x%02X\n", address);
        bme.setSampling(Adafruit_BME280::MODE_FORCED,
                       Adafruit_BME280::SAMPLING_X1,
                       Adafruit_BME280::SAMPLING_X1,
                       Adafruit_BME280::SAMPLING_X1,
                       Adafruit_BME280::FILTER_OFF);
        return true;
    }
    return false;
}

bool initSCD41() {
    scd4x.begin(Wire, 0x62);
    uint16_t error = scd4x.startPeriodicMeasurement();

    if (error == 0) {
        Serial.println("SCD41 sensor found");
        delay(200); // Warm-up time
        return true;
    }
    return false;
}

bool initDS18B20() {
    if (oneWire.search(sensorAddress)) {
        Serial.println("DS18B20 sensor found");
        return true;
    }
    oneWire.reset_search();
    return false;
}

bool initializeSpecificSensor(uint8_t sensorType) {
    switch (sensorType) {
        case SENSOR_SHT40_0x44:
            return initSHT40(0x44);
        case SENSOR_SHT40_0x45:
            return initSHT40(0x45);
        case SENSOR_BME280_0x76:
            return initBME280(0x76);
        case SENSOR_BME280_0x77:
            return initBME280(0x77);
        case SENSOR_SCD41:
            return initSCD41();
        case SENSOR_DS18B20:
            return initDS18B20();
        default:
            return false;
    }
}

bool autoDetectSensor() {
    Serial.println("Auto-detecting sensors...");

    // Try SHT40 0x44
    if (initializeSpecificSensor(SENSOR_SHT40_0x44)) {
        config.sensorType = SENSOR_SHT40_0x44;
        return true;
    }

    // Try SHT40 0x45
    if (initializeSpecificSensor(SENSOR_SHT40_0x45)) {
        config.sensorType = SENSOR_SHT40_0x45;
        return true;
    }

    // Try BME280 0x76
    if (initializeSpecificSensor(SENSOR_BME280_0x76)) {
        config.sensorType = SENSOR_BME280_0x76;
        return true;
    }

    // Try BME280 0x77
    if (initializeSpecificSensor(SENSOR_BME280_0x77)) {
        config.sensorType = SENSOR_BME280_0x77;
        return true;
    }

    // Try SCD41
    if (initializeSpecificSensor(SENSOR_SCD41)) {
        config.sensorType = SENSOR_SCD41;
        return true;
    }

    // Try DS18B20
    if (initializeSpecificSensor(SENSOR_DS18B20)) {
        config.sensorType = SENSOR_DS18B20;
        return true;
    }

    Serial.println("WARNING: No sensor detected!");
    return false;
}

bool initializeSensors() {
    Serial.println("Initializing sensors...");

    // Power up sensors
    pinMode(PWR_PIN, OUTPUT);
    digitalWrite(PWR_PIN, HIGH);
    delay(100);

    // Initialize I2C
    Wire.begin(config.sdaPin, config.sclPin);

    // Initialize OneWire
    ds18b20.begin();

    // Try to detect sensors
    if (config.sensorType == SENSOR_AUTO_DETECT) {
        return autoDetectSensor();
    } else {
        return initializeSpecificSensor(config.sensorType);
    }
}

bool readSHT40(SensorData& data) {
    uint16_t error = sht4x.measureHighPrecision(data.temperature, data.humidity);
    if (error != 0) {
        Serial.println("Error reading SHT40 sensor");
        return false;
    }
    return true;
}

bool readBME280(SensorData& data) {
    bme.takeForcedMeasurement();
    data.temperature = bme.readTemperature();
    data.humidity = bme.readHumidity();
    data.pressure = bme.readPressure() / 100.0f;
    return true;
}

bool readSCD41(SensorData& data) {
    uint16_t error = scd4x.readMeasurement(data.co2, data.temperature, data.humidity);
    if (error != 0) {
        Serial.println("Error reading SCD41 sensor");
        return false;
    }
    return true;
}

bool readDS18B20(SensorData& data) {
    ds18b20.requestTemperatures();
    data.temperature = ds18b20.getTempC(sensorAddress);

    if (data.temperature == DEVICE_DISCONNECTED_C) {
        Serial.println("Error reading DS18B20 sensor");
        return false;
    }
    return true;
}

SensorData readSensorData() {
    SensorData data;

    switch (config.sensorType) {
        case SENSOR_SHT40_0x44:
        case SENSOR_SHT40_0x45:
            data.isValid = readSHT40(data);
            break;
        case SENSOR_BME280_0x76:
        case SENSOR_BME280_0x77:
            data.isValid = readBME280(data);
            break;
        case SENSOR_SCD41:
            data.isValid = readSCD41(data);
            break;
        case SENSOR_DS18B20:
            data.isValid = readDS18B20(data);
            break;
        default:
            data.isValid = false;
    }

    if (data.isValid) {
        data.isValid = data.validateData();
    }

    return data;
}

// ============================================================================
// NETWORK MANAGEMENT FUNCTIONS
// ============================================================================

void handleConnectionFailure() {
    failedConnectAttempts++;

    if (failedConnectAttempts >= MAX_WIFI_CONNECT_ATTEMPTS) {
        Serial.printf("Max connection attempts reached (%d). Entering emergency sleep.\n",
                     MAX_WIFI_CONNECT_ATTEMPTS);
        enterSleep(EMERGENCY_SLEEP_TIME);
    }

    // Progressive backoff
    uint16_t sleepMinutes = min(static_cast<uint16_t>(config.sleepTime),
                               static_cast<uint16_t>(pow(2, failedConnectAttempts - 1)));
    sleepMinutes = max(sleepMinutes, static_cast<uint16_t>(1));

    Serial.printf("Connection failed (attempt %d/%d). Sleeping for %d minutes.\n",
                 failedConnectAttempts, MAX_WIFI_CONNECT_ATTEMPTS, sleepMinutes);

    enterSleep(sleepMinutes);
}

bool connectToWiFi() {
    if (WiFi.status() == WL_CONNECTED) {
        return true;
    }

    WiFi.mode(WIFI_STA);
    wifiManager.setConnectTimeout(25);

    Serial.println("Attempting to connect to WiFi...");

    if (!wifiManager.autoConnect("Laskakit Meteo Mini Config")) {
        Serial.println("Failed to connect to WiFi");
        handleConnectionFailure();
        return false;
    }

    Serial.println("Connected to WiFi");
    Serial.printf("IP address: %s\n", WiFi.localIP().toString().c_str());
    failedConnectAttempts = 0;
    return true;
}

bool sendToTMEP(const SensorData& data, float batteryVoltage) {
    if (config.serverSendType == SERVER_THINGSPEAK ||
        config.serverSendType == SERVER_NONE) {
        return true; // Skip TMEP
    }

    HTTPClient http;
    char url[256];

    // Build base URL with temperature and RSSI
    int urlLen = snprintf(url, sizeof(url), "http://%s/?temp=%.2f&rssi=%d",
                         config.serverAddressTmep, data.temperature, WiFi.RSSI());

    // Add additional parameters based on sensor type
    if (config.sensorType <= 4 && urlLen < sizeof(url) - 20) {
        urlLen += snprintf(url + urlLen, sizeof(url) - urlLen, "&humV=%.2f", data.humidity);
    }

    if ((config.sensorType == SENSOR_BME280_0x76 ||
         config.sensorType == SENSOR_BME280_0x77) && urlLen < sizeof(url) - 25) {
        urlLen += snprintf(url + urlLen, sizeof(url) - urlLen, "&pressV=%.2f", data.pressure);
    }

    if (config.sensorType == SENSOR_SCD41 && urlLen < sizeof(url) - 15) {
        urlLen += snprintf(url + urlLen, sizeof(url) - urlLen, "&CO2=%d", data.co2);
    }

    // Add battery voltage
    if (urlLen < sizeof(url) - 20) {
        snprintf(url + urlLen, sizeof(url) - urlLen, "&voltage=%.2f", batteryVoltage);
    }

    http.begin(url);
    Serial.printf("TMEP URL: %s\n", url);
    int httpCode = http.GET();
    http.end();

    Serial.printf("TMEP HTTP response: %d\n", httpCode);
    return (httpCode > 0);
}

bool sendToThingSpeak(const SensorData& data, float batteryVoltage) {
    if (config.serverSendType == SERVER_TMEP ||
        config.serverSendType == SERVER_NONE) {
        return true; // Skip ThingSpeak
    }

    if (strlen(config.thingSpeakApiKey) == 0 || strlen(config.thingSpeakChannelId) == 0) {
        Serial.println("Missing ThingSpeak API key or Channel ID");
        return false;
    }

    HTTPClient http;
    const char* url = "http://api.thingspeak.com/update";
    char postData[512];

    // Build base POST data with API key and temperature
    int dataLen = snprintf(postData, sizeof(postData), "api_key=%s&field%d=%.2f",
                          config.thingSpeakApiKey, config.tsFieldTemp, data.temperature);

    // Add humidity for supported sensors
    if (config.sensorType <= 4 && dataLen < sizeof(postData) - 30) {
        dataLen += snprintf(postData + dataLen, sizeof(postData) - dataLen,
                           "&field%d=%.2f", config.tsFieldHum, data.humidity);
    }

    // Add pressure for BME280
    if ((config.sensorType == SENSOR_BME280_0x76 ||
         config.sensorType == SENSOR_BME280_0x77) && dataLen < sizeof(postData) - 30) {
        dataLen += snprintf(postData + dataLen, sizeof(postData) - dataLen,
                           "&field%d=%.2f", config.tsFieldPress, data.pressure);
    }

    // Add CO2 for SCD41
    if (config.sensorType == SENSOR_SCD41 && dataLen < sizeof(postData) - 25) {
        dataLen += snprintf(postData + dataLen, sizeof(postData) - dataLen,
                           "&field%d=%d", config.tsFieldCO2, data.co2);
    }

    // Add battery voltage
    if (dataLen < sizeof(postData) - 30) {
        snprintf(postData + dataLen, sizeof(postData) - dataLen,
                "&field%d=%.2f", config.tsFieldBatt, batteryVoltage);
    }

    http.begin(url);
    http.addHeader("Content-Type", "application/x-www-form-urlencoded");

    Serial.printf("ThingSpeak POST data: %s\n", postData);
    int httpCode = http.POST(postData);
    http.end();

    Serial.printf("ThingSpeak HTTP response: %d\n", httpCode);
    return (httpCode > 0);
}

// ============================================================================
// WIFI CONFIGURATION FUNCTIONS
// ============================================================================

void saveConfigFromParameters() {
    // Update config from parameters
    strcpy(config.serverAddressTmep, customParams[0]->getValue());
    strcpy(config.thingSpeakApiKey, customParams[1]->getValue());
    strcpy(config.thingSpeakChannelId, customParams[2]->getValue());
    config.sleepTime = atoi(customParams[3]->getValue());
    config.sensorType = atoi(customParams[4]->getValue());
    config.serverSendType = atoi(customParams[5]->getValue());
    config.sdaPin = atoi(customParams[6]->getValue());
    config.sclPin = atoi(customParams[7]->getValue());
    config.tsFieldTemp = atoi(customParams[8]->getValue());
    config.tsFieldHum = atoi(customParams[9]->getValue());
    config.tsFieldPress = atoi(customParams[10]->getValue());
    config.tsFieldCO2 = atoi(customParams[11]->getValue());
    config.tsFieldBatt = atoi(customParams[12]->getValue());

    // Validate and save
    if (!config.isValid()) {
        Serial.println("Invalid configuration received, using defaults");
        config.setDefaults();
    }

    saveConfig();
}

// Static buffers for parameter values to avoid String objects
static char sleepTimeStr[8];
static char sensorTypeStr[4];
static char serverTypeStr[4];
static char sdaPinStr[4];
static char sclPinStr[4];
static char tsFieldTempStr[4];
static char tsFieldHumStr[4];
static char tsFieldPressStr[4];
static char tsFieldCO2Str[4];
static char tsFieldBattStr[4];

void initializeParameters() {
    if (parametersInitialized) return;

    // Convert numeric values to strings using snprintf
    snprintf(sleepTimeStr, sizeof(sleepTimeStr), "%d", config.sleepTime);
    snprintf(sensorTypeStr, sizeof(sensorTypeStr), "%d", config.sensorType);
    snprintf(serverTypeStr, sizeof(serverTypeStr), "%d", config.serverSendType);
    snprintf(sdaPinStr, sizeof(sdaPinStr), "%d", config.sdaPin);
    snprintf(sclPinStr, sizeof(sclPinStr), "%d", config.sclPin);
    snprintf(tsFieldTempStr, sizeof(tsFieldTempStr), "%d", config.tsFieldTemp);
    snprintf(tsFieldHumStr, sizeof(tsFieldHumStr), "%d", config.tsFieldHum);
    snprintf(tsFieldPressStr, sizeof(tsFieldPressStr), "%d", config.tsFieldPress);
    snprintf(tsFieldCO2Str, sizeof(tsFieldCO2Str), "%d", config.tsFieldCO2);
    snprintf(tsFieldBattStr, sizeof(tsFieldBattStr), "%d", config.tsFieldBatt);

    customParams[0] = new WiFiManagerParameter("tmepServer", "TMEP.CZ address", config.serverAddressTmep, 40);
    customParams[1] = new WiFiManagerParameter("thingSpeakKey", "ThingSpeak API Key", config.thingSpeakApiKey, 40);
    customParams[2] = new WiFiManagerParameter("thingSpeakChannel", "ThingSpeak Channel ID", config.thingSpeakChannelId, 20);
    customParams[3] = new WiFiManagerParameter("sleepTime", "Sleep time (minutes)", sleepTimeStr, 6);
    customParams[4] = new WiFiManagerParameter("sensorType", "Sensor type (99=auto)", sensorTypeStr, 2);
    customParams[5] = new WiFiManagerParameter("serverType", "Server type (0=None,1=TMEP,2=ThingSpeak,3=Both)", serverTypeStr, 2);
    customParams[6] = new WiFiManagerParameter("sdaPin", "I2C SDA pin", sdaPinStr, 3);
    customParams[7] = new WiFiManagerParameter("sclPin", "I2C SCL pin", sclPinStr, 3);
    customParams[8] = new WiFiManagerParameter("tsFieldTemp", "ThingSpeak field - Temperature", tsFieldTempStr, 2);
    customParams[9] = new WiFiManagerParameter("tsFieldHum", "ThingSpeak field - Humidity", tsFieldHumStr, 2);
    customParams[10] = new WiFiManagerParameter("tsFieldPress", "ThingSpeak field - Pressure", tsFieldPressStr, 2);
    customParams[11] = new WiFiManagerParameter("tsFieldCO2", "ThingSpeak field - CO2", tsFieldCO2Str, 2);
    customParams[12] = new WiFiManagerParameter("tsFieldBatt", "ThingSpeak field - Battery", tsFieldBattStr, 2);

    parametersInitialized = true;
}

void setupWiFiManager() {
    initializeParameters();

    // Add all parameters
    for (int i = 0; i < 13; i++) {
        wifiManager.addParameter(customParams[i]);
    }

    // Configure WiFiManager
    wifiManager.setSaveConfigCallback([]() {
        Serial.println("Configuration saved via WiFiManager");
        saveConfigFromParameters();
    });

    wifiManager.setAPCallback([](WiFiManager* wm) {
        Serial.println("Entered config mode");
        startLEDBlinking();
        portalRunning = true;
    });

    wifiManager.setWebServerCallback([]() {
        updateLEDBlink();
    });

    wifiManager.setParamsPage(true);
    wifiManager.setBreakAfterConfig(true);
    wifiManager.setDarkMode(true);
    wifiManager.setTitle("Laskakit Meteo Mini - Configuration");
    wifiManager.setHostname("LaskakitMeteoMini");
    wifiManager.setShowPassword(true);
    wifiManager.setHttpPort(80);
    wifiManager.setWiFiAPChannel(1);

    // Set timeout based on power source
    uint16_t timeout = isRunningOnBattery() ?
                      CONFIG_TIMEOUT_BATTERY : CONFIG_TIMEOUT_EXTERNAL;
    wifiManager.setConfigPortalTimeout(timeout);

    // Custom menu - using array instead of vector for better memory management
    const char* menu[] = {"wifi", "param", "info", "sep", "restart", "sep", "erase", "exit"};
    wifiManager.setMenu(menu, 8);
}

bool startConfigPortal() {
    setupWiFiManager();
    startLEDBlinking();
    portalRunning = true;

    bool result = wifiManager.startConfigPortal("Laskakit Meteo Mini Config");

    portalRunning = false;
    stopLEDBlinking();

    if (result) {
        Serial.println("WiFi configuration completed successfully");
    } else {
        Serial.println("WiFi configuration failed or timed out");
    }

    return result;
}

// ============================================================================
// BUTTON HANDLER FUNCTIONS
// ============================================================================

void checkButton() {
    // Handle portal if running
    if (portalRunning) {
        wifiManager.process();
        updateLEDBlink();

        // Auto-close portal after timeout
        if (millis() - portalStartTime > PORTAL_AUTO_CLOSE_TIME) {
            Serial.println("Portal auto-close timeout reached");
            wifiManager.stopWebPortal();
            portalRunning = false;
            stopLEDBlinking();
        }
    }

    // Check button press
    if (digitalRead(TRIGGER_PIN) == LOW) {
        delay(50); // Debounce
        if (digitalRead(TRIGGER_PIN) == LOW) {
            if (!portalRunning) {
                Serial.println("Button pressed - starting config portal");
                if (!startConfigPortal() && isRunningOnBattery()) {
                    enterSleep(config.sleepTime);
                }
            } else {
                Serial.println("Button pressed - stopping config portal");
                wifiManager.stopWebPortal();
                portalRunning = false;
                stopLEDBlinking();
            }
        }
    }
}

// ============================================================================
// MAIN FUNCTIONS
// ============================================================================

void setup() {
    Serial.begin(115200);
    delay(100);

    // Initialize hardware
    initLED();
    pinMode(TRIGGER_PIN, INPUT_PULLUP);

    // Check for emergency low battery
    if (isBatteryLow()) {
        enterEmergencyMode();
    }

    // Boot information
    bootCount++;
    esp_sleep_wakeup_cause_t wakeupReason = esp_sleep_get_wakeup_cause();

    Serial.println("\n=== Laskakit Meteo Mini v2.0 (Arduino Compatible) ===");
    Serial.printf("Boot count: %d\n", bootCount);
    Serial.printf("Battery voltage: %.2fV\n", getBatteryVoltage());
    Serial.printf("Power source: %s\n", isRunningOnBattery() ? "Battery" : "External");

    switch (wakeupReason) {
        case ESP_SLEEP_WAKEUP_TIMER:
            Serial.println("Wakeup: Timer");
            break;
        case ESP_SLEEP_WAKEUP_GPIO:
            Serial.println("Wakeup: Button (GPIO)");
            break;
        default:
            Serial.println("Wakeup: Power on or reset");
            break;
    }

    // Initialize EEPROM and load configuration
    EEPROM.begin(EEPROM_SIZE);
    loadConfig();
    printConfig();

    // Check for configuration mode (button pressed at startup)
    delay(100);
    if (wakeupReason == ESP_SLEEP_WAKEUP_GPIO || digitalRead(TRIGGER_PIN) == LOW) {
        Serial.println("Entering configuration mode...");
        delay(500);
        WiFi.mode(WIFI_STA);

        if (!startConfigPortal()) {
            if (isRunningOnBattery()) {
                Serial.println("Config failed on battery - entering sleep");
                enterSleep(config.sleepTime);
            }
        } else {
            Serial.println("Configuration completed - continuing with normal operation");
        }
    }

    // Connect to WiFi
    if (!connectToWiFi()) {
        // Connection failed, sleep handled in connectToWiFi
        return;
    }

    // Initialize sensors
    if (!initializeSensors()) {
        Serial.println("WARNING: Sensor initialization failed");
    }

    Serial.println("Setup completed successfully");
}

void loop() {
    // Handle button and portal
    checkButton();

    // Skip measurement if portal is running
    if (portalRunning) {
        return;
    }

    // Check WiFi connection
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("WiFi disconnected - will reconnect on next boot");
        enterSleep(config.sleepTime);
        return;
    }

    // Read sensor data
    Serial.println("Reading sensor data...");
    SensorData sensorData = readSensorData();
    float batteryVoltage = getBatteryVoltage();

    // Print sensor data
    Serial.printf("Temperature: %.2f°C\n", sensorData.temperature);
    if (config.sensorType <= 4) {
        Serial.printf("Humidity: %.2f%%\n", sensorData.humidity);
    }
    if (config.sensorType == SENSOR_BME280_0x76 || config.sensorType == SENSOR_BME280_0x77) {
        Serial.printf("Pressure: %.2f hPa\n", sensorData.pressure);
    }
    if (config.sensorType == SENSOR_SCD41) {
        Serial.printf("CO2: %d ppm\n", sensorData.co2);
    }
    Serial.printf("Battery: %.2fV\n", batteryVoltage);

    // Validate battery voltage (allow up to 6V for external power like USB)
    if (isnan(batteryVoltage) || batteryVoltage < 0.0f || batteryVoltage > 6.0f) {
        Serial.printf("Invalid battery voltage: %.2fV - skipping data transmission\n", batteryVoltage);
        sensorData.isValid = false;
    } else {
        Serial.printf("Battery voltage validation passed: %.2fV\n", batteryVoltage);
    }

    // Send data if valid
    bool sendSuccess = false;
    if (sensorData.isValid) {
        bool tmepSuccess = sendToTMEP(sensorData, batteryVoltage);
        bool thingSpeakSuccess = sendToThingSpeak(sensorData, batteryVoltage);

        sendSuccess = tmepSuccess || thingSpeakSuccess;

        if (sendSuccess) {
            Serial.println("Data sent successfully");
        } else {
            Serial.println("Failed to send data to any server");
        }
    } else {
        Serial.println("Invalid sensor data - skipping transmission");
    }

    // Determine sleep duration
    uint16_t sleepDuration = config.sleepTime;
    if (!sendSuccess && isRunningOnBattery()) {
        // Shorter sleep on battery when send fails
        sleepDuration = min(sleepDuration, static_cast<uint16_t>(15));
        Serial.printf("Send failed on battery - shorter sleep: %d minutes\n", sleepDuration);
    }

    // Enter sleep mode
    Serial.printf("Entering sleep for %d minutes\n", sleepDuration);
    enterSleep(sleepDuration);
}
